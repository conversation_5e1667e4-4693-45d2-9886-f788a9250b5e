import { useEffect, useState } from 'react';

import {
  ArrowBack as ArrowBackIcon,
  FilterList as FilterListIcon,
} from '@mui/icons-material';
import {
  Box,
  Button,
  Grid,
  Menu,
  MenuItem,
  Snackbar,
  Typography,
} from '@mui/material';
import { withStyles } from '@mui/styles';
import { DatePicker as KeyboardDatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';

import { useSiteValue } from 'App/SiteContext';
import { getFirebaseAuctionId } from 'Services/auctions';
import { getMandiConfig } from 'Services/common';
import { getDate } from 'Utilities/dateUtils';

import {
  ArrowWrapper,
  FilterButton,
  LeftSection,
  PageTitleWrapper,
  RightSection,
  TitleWrapper,
} from './styled';

const StyledMenu = withStyles({
  paper: {
    border: '1px solid #d3d4d5',
  },
})(props => (
  <Menu
    elevation={0}
    anchorOrigin={{
      vertical: 'bottom',
      horizontal: 'center',
    }}
    transformOrigin={{
      vertical: 'top',
      horizontal: 'center',
    }}
    {...props}
  />
));

const PageTitle = ({
  showBackHandler,
  showAuctionDate = true,
  disableAuctionDate = false,
  showFilterHandler,
  isFilterChanged,
  title,
  titleHelper,
  children,
  showDate = false,
  sequenceDate,
  leftSectionFullWidth,
}) => {
  const { mandiId, auctionDate, setAuctionDate, setMandiConfig } =
    useSiteValue();

  useEffect(() => {
    if (mandiId) {
      getMandiConfig({ auction_date: auctionDate, mandi_id: mandiId }).then(
        ({ responseData }) => setMandiConfig(responseData)
      );
    }
  }, [auctionDate, mandiId]);

  return (
    <PageTitleWrapper elevation={0} square>
      <Grid container style={{ alignItems: 'center' }}>
        <Grid item sm={leftSectionFullWidth ? 12 : 6}>
          <LeftSection>
            <TitleWrapper>
              {showBackHandler && (
                <ArrowWrapper onClick={showBackHandler}>
                  <ArrowBackIcon />
                </ArrowWrapper>
              )}
              <Typography variant='h6' component='h2'>
                <b>{title}</b>
              </Typography>
              {!!titleHelper && titleHelper}
            </TitleWrapper>
            <Box sx={{ display: { xs: 'none', md: 'block' } }}>
              <FilterButton>
                {!!showFilterHandler && (
                  <FilterListIcon
                    data-cy='mandi.filterList'
                    width='2em'
                    height='2em'
                    onClick={showFilterHandler}
                  />
                )}
              </FilterButton>
            </Box>
          </LeftSection>
        </Grid>
        <Grid
          item
          sm={6}
          style={{ display: 'flex', justifyContent: 'flex-end', width: '100%' }}
        >
          <RightSection
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-end',
              width: '100%',
            }}
          >
            {children && children}
            {showDate && (
              <Typography style={{ display: 'flex', alignItems: 'center' }}>
                {getDate(+sequenceDate)}
              </Typography>
            )}
            {showAuctionDate && (
              <Box
                sx={{
                  position: 'absolute',
                  top: '50%',
                  right: '16px',
                  transform: 'translateY(-50%)',
                  zIndex: 1000,
                }}
              >
                <KeyboardDatePicker
                  name='auction_date'
                  size='small'
                  label='Auction Date'
                  variant='inline'
                  autoOk
                  inputVariant='outlined'
                  format='DD/MM/YYYY'
                  color='primary'
                  margin='normal'
                  disabled={disableAuctionDate}
                  value={auctionDate ? dayjs(auctionDate) : null}
                  onChange={value =>
                    setAuctionDate(
                      dayjs(
                        value?.set('hour', 0).set('minute', 0).set('second', 0)
                      ).valueOf()
                    )
                  }
                  slotProps={{
                    textField: {
                      size: 'small',
                      style: {
                        color: 'red',
                      },
                      inputProps: {
                        'data-cy': 'mandi.auctionDate',
                      },
                    },
                  }}
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              </Box>
            )}
          </RightSection>
        </Grid>
      </Grid>
      <Box sx={{ display: { xs: 'block', sm: 'none' } }}>
        <FilterButton active={isFilterChanged}>
          {!!showFilterHandler && (
            <FilterListIcon
              data-cy='mandi.filterList'
              width='2em'
              onClick={showFilterHandler}
            />
          )}
        </FilterButton>
      </Box>
    </PageTitleWrapper>
  );
};

export default PageTitle;

import React, { useRef } from 'react';

import { Button, Dialog, DialogActions, DialogContent } from '@mui/material';
import PropTypes from 'prop-types';
import { useReactToPrint } from 'react-to-print';

import PrintTokenSlipPriceCard from './PrintTokenSlipPriceCard';

/**
 * Wrapper component for PrintTokenSlipPriceCard
 * Using react-to-print v3 API
 */
const PrintTokenSlipWrapper = ({ open, onClose, priceSlipData }) => {
  const componentRef = useRef();
  
  const handlePrint = useReactToPrint({
    contentRef: componentRef,  // v3 uses contentRef, not content
    documentTitle: 'Price Slip',
  });
  
  return (
    <Dialog
      maxWidth='md'
      fullWidth
      open={open}
      onClose={onClose}
      PaperProps={{
        'data-cy': 'mandi.priceSlip.previewModal'
      }}
    >
      <DialogContent>
        <div ref={componentRef} style={{ width: '100%' }}>
          {priceSlipData && <PrintTokenSlipPriceCard {...priceSlipData} />}
        </div>
      </DialogContent>
      <DialogActions>
        <Button
          color='primary'
          size='small'
          onClick={handlePrint}
          variant='contained'
          data-cy='mandi.priceSlip.printButton'
        >
          Print
        </Button>
      </DialogActions>
    </Dialog>
  );
};

PrintTokenSlipWrapper.displayName = 'PrintTokenSlipWrapper';

PrintTokenSlipWrapper.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  priceSlipData: PropTypes.object,
};

export default PrintTokenSlipWrapper;

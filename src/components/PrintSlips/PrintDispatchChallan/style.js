import styled from 'styled-components';

// Container
export const Container = styled.div`
  width: 210mm;
  min-height: 297mm;
  margin: 0 auto;
  padding: 10mm;
  background: white;
  border: 2px solid black;
  font-family: Arial, sans-serif;
  font-size: 10px;
  line-height: 1.3;
  page-break-after: always;
  box-sizing: border-box;

  @media print {
    width: 210mm;
    min-height: 297mm;
    margin: 0;
    padding: 10mm;
    border: 2px solid black;
    page-break-after: always;
    box-sizing: border-box;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  @page {
    size: A4;
    margin: 0;
  }
`;

// Company Header Components
export const CompanyHeader = styled.div`
  text-align: center;
  margin-bottom: 20px;
  border-bottom: 2px solid black;
  padding-bottom: 10px;
`;

export const CompanyName = styled.h1`
  font-size: 18px;
  font-weight: bold;
  margin: 0 0 8px 0;
  letter-spacing: 1px;
`;

export const CompanyAddress = styled.div`
  font-size: 11px;
  line-height: 1.4;
  margin: 0;
`;

// Info Grid Components
export const InfoGrid = styled.div`
  margin-bottom: 15px;
`;

export const InfoRow = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 10px;
`;

export const InfoLabel = styled.div`
  font-weight: bold;
  min-width: 80px;
  font-size: 10px;
`;

export const InfoValue = styled.div`
  border-bottom: 1px solid black;
  padding: 2px 5px;
  min-height: 16px;
  flex: 1;
  font-size: 10px;
`;

// Vehicle Section Components
export const VehicleSection = styled.div`
  margin-bottom: 15px;
  border-top: 1px solid #ccc;
  padding-top: 10px;
`;

export const VehicleGrid = styled.div``;

// Table Section Components
export const TableSection = styled.div`
  margin: 15px 0;
  position: relative;
  background: transparent;

  /* Watermark */
  .watermark {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 120px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.05);
    z-index: 1;
    pointer-events: none;
    user-select: none;
  }
`;

export const MainTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  border: 2px solid black;
  position: relative;
  z-index: 2;
  background: transparent;

  th,
  td {
    border: 1px solid black;
    padding: 4px;
    text-align: center;
    vertical-align: middle;
    font-size: 9px;
    background: transparent;
  }

  th {
    background-color: #f0f0f0;
    font-weight: bold;
    font-size: 9px;
    line-height: 1.1;
    height: 35px;
  }

  .grand-total-row {
    background-color: #f5f5f5;
    font-weight: bold;
  }

  .grand-total-row td {
    font-size: 10px;
    padding: 6px 4px;
  }
`;

// Footer Section Components
export const FooterSection = styled.div`
  margin-top: 20px;
  border-top: 1px solid #ccc;
  padding-top: 15px;
`;

export const FooterGrid = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 10px;
`;

export const FooterItem = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
`;

// Note Section
export const NoteSection = styled.div`
  margin: 20px 0 15px 0;
  font-size: 11px;
`;

// Signature Section Components
export const SignaturesSection = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
  padding-top: 30px;
  margin-top: 20px;
  gap: 20px;
`;

export const SignatureBox = styled.div`
  flex: 1;
  text-align: center;
`;

export const SignatureLabel = styled.span`
  display: block;
  text-align: center;
  font-size: 10px;
  color: #333;
  margin-bottom: 20px;
  line-height: 1.3;
  font-weight: 500;
`;

export const SignatureLine = styled.div`
  border-bottom: 1px solid #000;
  width: 100%;
  margin-top: 10px;
`;

// Timestamp Section Components
export const TimestampSection = styled.div`
  text-align: center;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #ccc;
  font-size: 9px;
  color: #666;
  font-style: italic;
`;

// Header Section (if needed for future use)
export const HeaderSection = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
`;

import React from 'react';

import appleImage from '../../../assets/apple.png';
import fruitXImage from '../../../assets/FruitX.png';
import { getDate } from '../../../utilities/dateUtils';

import {
  Container,
  HeaderSection,
  CompanyHeader,
  CompanyName,
  CompanyAddress,
  InfoGrid,
  InfoRow,
  InfoLabel,
  InfoValue,
  VehicleSection,
  VehicleGrid,
  TableSection,
  MainTable,
  FooterSection,
  FooterGrid,
  FooterItem,
  SignaturesSection,
  SignatureBox,
  SignatureLabel,
  SignatureLine,
  TimestampSection,
  NoteSection,
} from './style';

const PrintDispatchChallan = ({
  consignor,
  consignee,
  challanNo,
  date,
  totalBoxes,
  productName,
  fromLocation,
  toLocation,
  vehicleNo,
  driverName,
  driverNo,
  ownerName,
  vehicleType,
  lotData,
  totalCases,
  freightPerBox,
  totalFreight,
  advance,
  totalFreightInWords,
  brandLogo,
  gradeInfo = {}, // Grade information with sku_id, sku, unit
  mandiName = 'FRUITX NARKANDA MANDI', // Mandi name prop
  mandiAddress = 'Kaithla Mod, Narkanda, Near Hotel Shyam Rejency, Tehsil - Kumarsain, Distt.- Shimla, Himachal Pradesh- 171213', // Mandi address prop
}) => {
  // Function to generate grade columns from lotData
  const generateGradeColumns = () => {
    if (!lotData || lotData.length === 0) return [];

    // Get union of all grade sku_ids from all lots
    const allGradeSkuIds = new Set();
    lotData.forEach(lot => {
      if (lot.items && Array.isArray(lot.items)) {
        lot.items.forEach(item => {
          if (item.sku_id) {
            allGradeSkuIds.add(item.sku_id);
          }
        });
      }
    });

    // Convert to array and create column objects
    // We'll use the first occurrence of each sku_id to get the grade and unit info
    const gradeColumns = [];
    allGradeSkuIds.forEach(skuId => {
      // Find the first item with this sku_id to get its details
      let gradeInfo = null;
      for (const lot of lotData) {
        if (lot.items && Array.isArray(lot.items)) {
          const item = lot.items.find(item => item.sku_id === skuId);
          if (item) {
            gradeInfo = item;
            break;
          }
        }
      }

      if (gradeInfo) {
        gradeColumns.push({
          key: skuId,
          label: gradeInfo.grade || skuId,
          subLabel: '', // Remove subLabel to show only grade name
          sku_id: skuId,
        });
      }
    });

    return gradeColumns;
  };

  // Generate grade columns dynamically
  const gradeColumns = generateGradeColumns();

  // Function to format current timestamp
  const getCurrentTimestamp = () => {
    const now = new Date();
    const options = {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true,
      timeZone: 'Asia/Calcutta',
    };
    return now.toLocaleString('en-IN', options);
  };

  const calculateLotAmount = lot => {
    if (!lot.items || !Array.isArray(lot.items)) return 0;
    return lot.items.reduce((total, item) => {
      const quantity = parseInt(item.unit) || 0;
      const price = parseInt(item.price) || 0;
      return total + quantity * price;
    }, 0);
  };

  const renderDataRows = () => {
    if (!lotData) return null;

    return lotData
      .map((lot, index) => {
        const lotAmount = calculateLotAmount(lot);
        return [
          // First row - QTY
          <tr key={`${index}-qty`}>
            <td rowSpan={2}>{lot.lot || ''}</td>
            <td rowSpan={2}>{lot.productVariety || ''}</td>
            <td rowSpan={2}>{lot.grade || ''}</td>
            <td>Qty</td>
            {gradeColumns.map(column => {
              // Find the item with matching sku_id in this lot
              const item = lot.items?.find(item => item.sku_id === column.key);
              return <td key={column.key}>{item?.unit || '-'}</td>;
            })}
            <td rowSpan={2}>{lot.totalBoxes || ''}</td>
            <td rowSpan={2}>{lotAmount.toLocaleString()}</td>
          </tr>,
          // Second row - Price
          <tr key={`${index}-price`}>
            <td>Price</td>
            {gradeColumns.map(column => {
              // Find the item with matching sku_id in this lot
              const item = lot.items?.find(item => item.sku_id === column.key);
              return <td key={column.key}>{item?.price || '-'}</td>;
            })}
          </tr>,
        ];
      })
      .flat();
  };

  const calculateGrandTotal = () => {
    if (!lotData) return 0;
    return lotData.reduce((total, lot) => {
      return total + (parseInt(lot.totalBoxes) || 0);
    }, 0);
  };

  const calculateGrandTotalAmount = () => {
    if (!lotData) return 0;
    return lotData.reduce((total, lot) => {
      return total + calculateLotAmount(lot);
    }, 0);
  };

  return (
    <Container>
      {/* Company Header */}
      <CompanyHeader
        style={{
          marginBottom: '0px',
          paddingBottom: '0px',
          borderBottom: 'none',
        }}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '5px',
            border: '2px solid black',
          }}
        >
          <div style={{ flex: '0 0 80px', textAlign: 'center' }}>
            <img
              src={appleImage}
              alt='Apple'
              style={{ width: '50px', height: '50px', objectFit: 'contain' }}
            />
          </div>
          <div style={{ textAlign: 'center', flex: 1, padding: '0 15px' }}>
            <CompanyName
              style={{
                fontSize: '18px',
                fontWeight: 'bold',
                margin: '0 0 4px 0',
              }}
            >
              {mandiName}
            </CompanyName>
            <CompanyAddress style={{ fontSize: '10px', lineHeight: '1.2' }}>
              {mandiAddress}
            </CompanyAddress>
          </div>
          <div style={{ flex: '0 0 80px', textAlign: 'center' }}>
            <img
              src={fruitXImage}
              alt='FruitX Logo'
              style={{
                width: '70px',
                height: '40px',
                objectFit: 'contain',
                marginBottom: '2px',
              }}
            />
            <div style={{ fontSize: '8px', fontWeight: 'bold', color: '#333' }}>
              Chifu Agritech Pvt. Ltd.
            </div>
          </div>
        </div>
      </CompanyHeader>

      {/* Combined Basic Info and Vehicle Section */}
      <TableSection style={{ margin: '0px' }}>
        <MainTable style={{ border: '1px solid black' }}>
          <tbody>
            <tr>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'left',
                  padding: '8px',
                }}
              >
                Consignor:
              </td>
              <td style={{ textAlign: 'left', padding: '8px' }} colSpan='3'>
                {consignor || ''}
              </td>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'left',
                  padding: '8px',
                }}
              >
                Challan No
              </td>
              <td style={{ textAlign: 'left', padding: '8px' }}>
                {challanNo || ''}
              </td>
            </tr>
            <tr>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'left',
                  padding: '8px',
                }}
              >
                Consignee:
              </td>
              <td style={{ textAlign: 'left', padding: '8px' }} colSpan='3'>
                {consignee || 'CHIFU AGRITECH PRIVATE LIMITED'}
              </td>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'left',
                  padding: '8px',
                }}
              >
                Date
              </td>
              <td style={{ textAlign: 'left', padding: '8px' }}>
                {date ? getDate(date) : ''}
              </td>
            </tr>
            <tr>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'left',
                  padding: '8px',
                }}
              >
                Total Boxes
              </td>
              <td style={{ textAlign: 'left', padding: '8px' }}>
                {totalBoxes || ''}
              </td>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'left',
                  padding: '8px',
                }}
              >
                Product Name
              </td>
              <td style={{ textAlign: 'left', padding: '8px' }}>
                {productName || 'APPLE'}
              </td>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'left',
                  padding: '8px',
                }}
              >
                From
              </td>
              <td style={{ textAlign: 'left', padding: '8px' }}>
                {fromLocation || ''}
              </td>
            </tr>
            <tr>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'left',
                  padding: '8px',
                }}
              >
                Vehicle No.
              </td>
              <td style={{ textAlign: 'left', padding: '8px' }}>
                {vehicleNo || ''}
              </td>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'left',
                  padding: '8px',
                }}
              >
                Driver Name
              </td>
              <td style={{ textAlign: 'left', padding: '8px' }}>
                {driverName || ''}
              </td>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'left',
                  padding: '8px',
                }}
              >
                To
              </td>
              <td style={{ textAlign: 'left', padding: '8px' }}>
                {toLocation || ''}
              </td>
            </tr>
            <tr>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'left',
                  padding: '8px',
                }}
              >
                Driver No.
              </td>
              <td style={{ textAlign: 'left', padding: '8px' }}>
                {driverNo || ''}
              </td>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'left',
                  padding: '8px',
                }}
              >
                Owner Name
              </td>
              <td style={{ textAlign: 'left', padding: '8px' }}>
                {ownerName || ''}
              </td>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'left',
                  padding: '8px',
                }}
              >
                Vehicle Type
              </td>
              <td style={{ textAlign: 'left', padding: '8px' }}>
                {vehicleType || ''}
              </td>
            </tr>
          </tbody>
        </MainTable>
      </TableSection>

      {/* Main Table */}
      <TableSection style={{ margin: '0px' }}>
        <div className='watermark'>FRUITX</div>
        <MainTable>
          <thead>
            <tr>
              <th>Lot</th>
              <th>Product Variety</th>
              <th>Grade</th>
              <th>Qty/Price</th>
              {gradeColumns.map(column => (
                <th key={column.key}>
                  {column.label}
                  {column.subLabel && (
                    <>
                      <br />
                      {column.subLabel}
                    </>
                  )}
                </th>
              ))}
              <th>
                Total
                <br />
                Boxes
              </th>
              <th>Amount</th>
            </tr>
          </thead>
          <tbody>
            {renderDataRows()}
            <tr className='grand-total-row'>
              <td colSpan='4'>
                <strong>GRAND TOTAL</strong>
              </td>
              <td colSpan={gradeColumns.length}></td>
              <td>
                <strong>{calculateGrandTotal()}</strong>
              </td>
              <td>
                <strong>{calculateGrandTotalAmount().toLocaleString()}</strong>
              </td>
            </tr>
          </tbody>
        </MainTable>
      </TableSection>

      {/* Footer Section */}
      <TableSection
        style={{
          margin: '0px 0 0 0',
          borderTop: '1px solid #ccc',
        }}
      >
        <MainTable style={{ border: '1px solid black' }}>
          <tbody>
            <tr>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'left',
                  padding: '8px',
                }}
              >
                Total Cases:
              </td>
              <td style={{ textAlign: 'left', padding: '8px' }}>
                {totalCases || ''}
              </td>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'left',
                  padding: '8px',
                }}
              >
                Freight per Box(Rs.):
              </td>
              <td style={{ textAlign: 'left', padding: '8px' }}>
                {freightPerBox || ''}
              </td>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'left',
                  padding: '8px',
                }}
              >
                Total Freight(Rs.):
              </td>
              <td style={{ textAlign: 'left', padding: '8px' }}>
                {totalFreight || ''}
              </td>
            </tr>
            <tr>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'left',
                  padding: '8px',
                }}
              >
                Advance:
              </td>
              <td style={{ textAlign: 'left', padding: '8px' }}>
                {advance || ''}
              </td>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'left',
                  padding: '8px',
                }}
              >
                Total Freight(in words):
              </td>
              <td style={{ textAlign: 'left', padding: '8px', colSpan: 3 }}>
                {totalFreightInWords || ''}
              </td>
            </tr>
          </tbody>
        </MainTable>
      </TableSection>
      {/* Note Section */}
      <TableSection style={{ margin: '0px' }}>
        <MainTable style={{ border: '1px solid black' }}>
          <tbody>
            <tr>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'left',
                  padding: '8px',
                }}
              >
                Note:
              </td>
              <td style={{ textAlign: 'left', padding: '8px' }}>
                {/* Empty space for notes to be written */}
              </td>
            </tr>
          </tbody>
        </MainTable>
      </TableSection>

      {/* Signatures Section */}
      <TableSection style={{ margin: '0px' }}>
        <MainTable style={{ border: '1px solid black' }}>
          <tbody>
            <tr>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'center',
                  padding: '8px',
                  verticalAlign: 'bottom',
                  height: '60px',
                }}
              >
                Loading Supervisor Sign.
              </td>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'center',
                  padding: '8px',
                  verticalAlign: 'bottom',
                  height: '60px',
                }}
              >
                Driver Signature
              </td>
              <td
                style={{
                  fontWeight: 'bold',
                  backgroundColor: '#f0f0f0',
                  textAlign: 'center',
                  padding: '8px',
                  verticalAlign: 'bottom',
                  height: '60px',
                }}
              >
                Sender Signature
              </td>
            </tr>
          </tbody>
        </MainTable>
      </TableSection>

      {/* Timestamp Section */}
      <TimestampSection>Generated on {getCurrentTimestamp()}</TimestampSection>
    </Container>
  );
};

export default PrintDispatchChallan;

import React, { forwardRef } from 'react';

import { getDate } from '../../../utilities/dateUtils';

import {
  Container,
  HeaderSection,
  DateField,
  DateBox,
  DateText,
  TitleSection,
  MainTitle,
  LogoSection,
  BrandLogo,
  BrandText,
  FruitXText,
  XText,
  CompanyText,
  InfoGrid,
  InfoBox,
  InfoLabel,
  InfoValue,
  SignaturesSection,
  SignatureBox,
  SignatureLabel,
  SignatureLine,
  TimestampSection,
} from './style';

const PrintCustomerSlip = forwardRef(({
  tokenNumber,
  farmerName,
  mandiNo,
  date,
  brandLogo,
  lotData,
  pageData, // New prop for multiple pages
  skuSizes = [], // Add skuSizes prop for dynamic headers
  customerShortCode, // Add customer_short_code prop
}, ref) => {
  // Detailed logging for PDF component data verification
  // Function to format current timestamp
  const getCurrentTimestamp = () => {
    const now = new Date();
    const options = {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true,
      timeZone: 'Asia/Calcutta',
    };
    return now.toLocaleString('en-IN', options);
  };

  // Get grade columns from SKU sizes API response - completely dynamic
  const getGradeColumns = (lots) => {
    // If we have skuSizes from API, use them
    if (skuSizes && skuSizes.length > 0) {
      const sortedSkuSizes = skuSizes.sort((a, b) => a.id - b.id);
      return sortedSkuSizes;
    }
    
    // Fallback: extract from lot data if no skuSizes provided
    if (!lots || lots.length === 0) {
      return [];
    }
    
    const gradeKeys = new Set();
    lots.forEach(lot => {
      if (lot.grades) {
        Object.keys(lot.grades).forEach(gradeKey => {
          gradeKeys.add(gradeKey);
        });
      }
    });
    
    const fallbackColumns = Array.from(gradeKeys).sort().map(key => ({ id: key, size: key }));
    return fallbackColumns;
  };

  // Helper function to get header display name - use API data directly
  const getHeaderDisplay = (size) => {
    // Use the size directly from API - no hardcoded mapping needed
    return size;
  };

  const renderDataRows = (lots) => {
    if (!lots) return null;

    const gradeColumns = getGradeColumns(lots);
    const rows = [];

    lots.forEach((lot, index) => {
      // Quantity row
      rows.push(
        <tr key={`quantity-row-${index}`}>
          <td className='variety' rowSpan={2}>
            {lot.variety || ''}
          </td>
          <td className='lot' rowSpan={2}>
            <div>
              <div>{lot.lot || ''}</div>
              <div style={{ fontSize: '8px', marginTop: '2px', color: '#666' }}>
                {lot.packType || ''}
              </div>
            </div>
          </td>
          <td>Qty</td>
          {/* Dynamic grade columns from API */}
          {gradeColumns.map((column) => {
            const quantity = lot.grades?.[column.size]?.quantity;
            return (
              <td key={`qty-${column.id}`} className='grade-data'>
                {quantity || '-'}
              </td>
            );
          })}
        </tr>
      );

      // Price row
      rows.push(
        <tr key={`price-row-${index}`}>
          <td>Price-</td>
          {/* Dynamic grade columns from API */}
          {gradeColumns.map((column) => {
            const price = lot.grades?.[column.size]?.price;
            return (
              <td key={`price-${column.id}`} className='grade-data'>
                {price || '-'}
              </td>
            );
          })}
        </tr>
      );
    });
    return rows;
  };

  const renderSinglePage = (pageInfo, pageIndex, isLastPage) => {
    const gradeColumns = getGradeColumns(pageInfo.lotData);
    
    return (
      <Container key={`page-${pageIndex}`} style={{ pageBreakAfter: isLastPage ? 'auto' : 'always' }}>
        {/* Header Section */}
        <HeaderSection>
          {/* Date Field */}
          <DateField>
            <DateBox>
              <DateText>Date: {date ? getDate(date) : ''}</DateText>
            </DateBox>
          </DateField>

          {/* Title */}
          <TitleSection>
            <MainTitle>Customer Slip</MainTitle>
          </TitleSection>

          {/* Logo */}
          <LogoSection>
            {brandLogo ? (
              <BrandLogo src={brandLogo} alt='Brand Logo' />
            ) : (
              <>
                <BrandText>
                  <FruitXText>FRUIT</FruitXText>
                  <XText>X</XText>
                </BrandText>
                <CompanyText>Chifu Agritech Pvt. Ltd.</CompanyText>
              </>
            )}
          </LogoSection>
        </HeaderSection>

        {/* Info Section */}
        <InfoGrid>
          <InfoBox>
            <InfoLabel>Token No. (टोकन संख्या)</InfoLabel>
            <InfoValue>{tokenNumber || ''}</InfoValue>
          </InfoBox>

          <InfoBox>
            <InfoLabel>Farmer Name (किसान का नाम)</InfoLabel>
            <InfoValue>{farmerName || ''}</InfoValue>
          </InfoBox>

          <InfoBox>
            <InfoLabel>Customer Code</InfoLabel>
            <InfoValue>{pageInfo.customerShortCode || customerShortCode || ''}</InfoValue>
          </InfoBox>

          <InfoBox>
            <InfoLabel>Mandi No.</InfoLabel>
            <InfoValue>{pageInfo.mandiNo || ''}</InfoValue>
          </InfoBox>
        </InfoGrid>

        {/* Main Table */}
        <div className='main-table'>
          <div className='watermark'>FRUITX</div>
          <table className='customer-table'>
            <thead>
              <tr>
                <th rowSpan='3'>Variety</th>
                <th rowSpan='3'>Lot</th>
                <th></th>
                {/* Dynamic headers from API */}
                {gradeColumns.map((column) => (
                  <th key={`header-${column.id}`}>
                    {getHeaderDisplay(column.size)}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>{renderDataRows(pageInfo.lotData)}</tbody>
            <tfoot>
            </tfoot>
          </table>
        </div>

        {/* Footer Section */}
        <SignaturesSection>
          <SignatureBox>
            <SignatureLabel>
              Loader Name and Sign
              <br />
              (हस्ताक्षर)
              <br />
              {pageInfo.customerShortCode || customerShortCode || ''}
            </SignatureLabel>
            <SignatureLine></SignatureLine>
          </SignatureBox>

          <SignatureBox>
            <SignatureLabel>
              Supervisor Name and Sign
              <br />
              (हस्ताक्षर)
            </SignatureLabel>
            <SignatureLine></SignatureLine>
          </SignatureBox>
        </SignaturesSection>

        {/* Timestamp Section */}
        <TimestampSection>Generated on {getCurrentTimestamp()}</TimestampSection>
      </Container>
    );
  };

  // If pageData is provided, render multiple pages; otherwise, render single page (backward compatibility)
  if (pageData && pageData.length > 0) {
    return (
      <div ref={ref}>
        {pageData.map((pageInfo, index) => 
          renderSinglePage(pageInfo, index, index === pageData.length - 1)
        )}
      </div>
    );
  }

  // Single page rendering (new implementation and backward compatibility)
  return (
    <div ref={ref}>
      {renderSinglePage({ mandiNo, lotData }, 0, true)}
    </div>
  );
});

PrintCustomerSlip.displayName = 'PrintCustomerSlip';

export default PrintCustomerSlip;

import styled from 'styled-components';

// Header Section Components
export const HeaderSection = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
`;

export const DateField = styled.div`
  width: 25%;
`;

export const DateBox = styled.div`
  border: 1px solid black;
  padding: 0.3rem;
  display: flex;
  align-items: center;
  height: 2rem;
`;

export const DateText = styled.span`
  font-weight: 700;
  margin-right: 0.3rem;
  font-size: 14px;
`;

export const TitleSection = styled.div`
  width: 50%;
  text-align: center;
`;

export const MainTitle = styled.h1`
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
`;

export const LogoSection = styled.div`
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
`;

export const BrandLogo = styled.img`
  max-height: 40px;
`;

export const BrandText = styled.div`
  font-weight: bold;
  font-size: 2.2rem;
  letter-spacing: -0.05em;
`;

export const FruitXText = styled.span`
  color: #4caf50;
`;

export const XText = styled.span`
  color: #f44336;
`;

export const CompanyText = styled.p`
  font-weight: 400;
  font-size: 0.9rem;
  margin: 0;
  margin-top: 0px !important;
  width: 100%;
  text-align: center;
`;

// Info Grid Components
export const InfoGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 0.5rem;
  margin-bottom: 1rem;
`;

export const InfoBox = styled.div`
  display: flex;
  flex-direction: column;
`;

export const InfoLabel = styled.div`
  text-align: center;
  font-weight: 500;
  margin-bottom: 0.2rem;
  font-size: 13px;
`;

export const InfoValue = styled.div`
  border: 1px solid black;
  padding: 0.4rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  justify-content: center;
`;

// Signature Section Components
export const SignaturesSection = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
  padding-top: 20px;
  margin-top: 15px;
`;

export const SignatureBox = styled.div`
  width: 45%;
`;

export const SignatureLabel = styled.span`
  display: block;
  text-align: center;
  font-size: 14px;
  color: #333;
  margin-bottom: 15px;
  line-height: 1.3;
`;

export const SignatureLine = styled.div`
  border-bottom: 1px solid #000;
  width: 100%;
`;

// Timestamp Section Components
export const TimestampSection = styled.div`
  text-align: right;
  margin-top: 15px;
  padding-top: 10px;
  font-size: 13px;
  color: #666;
  font-style: italic;
`;

export const Container = styled.div`
  width: 220mm;
  margin: 20mm auto 0 auto;
  padding: 12mm;
  background: white;
  border: 2px solid black;
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  page-break-after: always;
  box-sizing: border-box;

  @media print {
    width: 220mm;
    margin: 15mm auto 0 auto;
    padding: 12mm;
    border: 2px solid black;
    page-break-after: always;
    box-sizing: border-box;
  }

  @page {
    margin: 10mm;
    size: A4;
  }

  /* Main Table */
  .main-table {
    margin: 10px 0;
    position: relative;
    background: transparent;
  }

  /* Watermark */
  .watermark {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 120px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.08);
    z-index: 1;
    pointer-events: none;
    user-select: none;
  }

  .customer-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid black;
    position: relative;
    z-index: 2;
    background: transparent;
    table-layout: fixed; /* Fixed table layout to prevent overflow */
  }

  .customer-table th,
  .customer-table td {
    border: 1px solid black;
    padding: 4px;
    text-align: center;
    vertical-align: middle;
    font-size: 13px;
    background: transparent;
    word-wrap: break-word; /* Break long words */
    overflow: hidden; /* Hide overflow */
    text-overflow: ellipsis; /* Show ellipsis for overflow */
  }

  .customer-table th {
    background-color: #f0f0f0;
    font-weight: bold;
    font-size: 13px;
    line-height: 1.2;
    height: 30px;
  }

  .mandi-no {
    font-weight: 600;
    font-size: 13px;
    width: 60px;
  }

  .variety {
    font-weight: 600;
    font-size: 13px;
    width: 90px;
  }

  .lot {
    font-size: 13px;
    width: 60px;
  }

  .grade-data {
    font-size: 12px;
    width: 40px;
    padding: 5px !important;
  }

  .separator-row {
    height: 10px;
    background-color: #f9f9f9;
  }

  .price-label {
    text-align: left !important;
    font-weight: 600;
    background-color: #f5f5f5;
    height: 30px;
  }

  /* Bold styling for token number and mandi number */
  .token-number-bold,
  .mandi-number-bold {
    font-weight: bold !important;
    justify-content: center;
  }

  /* Total count styling with spacing */
  .total-count {
    font-weight: bold;
    color: #666;
    margin-left: 2px;
  }
`;

import { useEffect, useState, forwardRef, useMemo } from 'react';

import PropTypes from 'prop-types';

import { getDate } from '../../../utilities/dateUtils';

import {
  Container,
  ContentWrapper,
  ContentSpacer,
  GeneratedTimestamp,
  HeaderSection,
  DateField,
  DateBox,
  DateText,
  TitleSection,
  MainTitle,
  SubTitle,
  LogoSection,
  BrandLogo,
  BrandText,
  FruitXText,
  XText,
  CompanyText,
  InfoGrid,
  InfoBox,
  InfoLabel,
  InfoValue,
  DetailsGrid,
  DetailsColumn,
  DetailRow,
  DetailLabel,
  DetailValue,
  SignaturesSection,
  SignatureBox,
  SignatureLabel,
  SignatureLine,
} from './style';

const PrintTokenSlipPriceCard = forwardRef(({
  data,
  auctionDate,
  brandLogo,
  mandiName,
  farmerName,
  transporterName,
  mobileNumber,
  vehicleNumber,
  address,
  tokenNumber,
  boxesBrought,
  amountData,
  skuSizes = [], 
  hasTransporterPayment = false,
}, ref) => {
  const [lotsData, setLotsData] = useState([]);

  const effectiveSkuSizes = useMemo(() => {
    // If we have SKU sizes from the API, use them (shows ALL configured sizes for the mandi)
    if (skuSizes && skuSizes.length > 0) {
      // Sort by ID in ascending order for consistent ordering
      return skuSizes.sort((a, b) => a.id - b.id);
    }
    
    // Fallback: Extract unique grade keys from the data to create headers (only if no SKU sizes available)
    const gradeKeys = new Set();
    (data || []).forEach(lot => {
      if (lot.grades) {
        Object.keys(lot.grades).forEach(gradeKey => {
          gradeKeys.add(gradeKey);
        });
      }
    });
    
    // Convert grade keys to fake SKU size objects for header generation
    const fallbackSizes = Array.from(gradeKeys)
      .sort() // Sort grade keys alphabetically first
      .map((gradeKey) => {
        // Convert from "L_100" format back to "L (100)" format for display
        let displayName = gradeKey;
        if (gradeKey.includes('_')) {
          const parts = gradeKey.split('_');
          if (parts.length === 2 && !isNaN(parts[1])) {
            displayName = `${parts[0]} (${parts[1]})`;
          }
        }
        
        return {
          id: gradeKey, // Use the string key as the ID for data lookup
          size: displayName
        };
      })
      .sort((a, b) => {
        // Sort by numeric weight if available, otherwise alphabetically
        const aWeight = a.size.match(/\((\d+)\)/)?.[1];
        const bWeight = b.size.match(/\((\d+)\)/)?.[1];
        
        if (aWeight && bWeight) {
          return parseInt(aWeight) - parseInt(bWeight);
        }
        
        return a.size.localeCompare(b.size);
      });
    
    return fallbackSizes;
  }, [skuSizes, data]);

  useEffect(() => {
    setLotsData(data || []);
  }, [data]);

  const renderDataRows = () => {
    const rows = [];

    // Render actual data rows
    lotsData.forEach((lot, index) => {
      // --- Row 1: Displays quantity and sets up shared cells ---
      // The first three <td>s and the last one will span across all three rows for this entry.
      rows.push(
        <tr key={`lot-${index}-quantity`}>
          <td className='mandi-no' rowSpan={3}>
            {lot.mandiNo || String(index + 1).padStart(3, '0')}
          </td>
          <td className='variety' rowSpan={3}>
            {lot.variety || ''}
          </td>
          <td className='lot' rowSpan={3}>
            <div>
              <div>{lot.lot || ''}</div>
              <div style={{ fontSize: '8px', marginTop: '2px', color: '#666' }}>
                {lot.packType || ''}
              </div>
            </div>
          </td>

          <td className='grade-data'>Qty</td>

          {/* Dynamic quantity data for each grade based on effectiveSkuSizes */}
          {effectiveSkuSizes.map((sku) => {
            // For SKU sizes from API, use the ID directly as the grade key
            // For fallback sizes (extracted from data), the ID is already the grade key
            const gradeKey = sku.id;
            
            return (
              <td key={`qty-${sku.id}`} className='grade-data'>
                {lot.grades?.[gradeKey]?.quantity || ''}
              </td>
            );
          })}

          <td className='amount' rowSpan={3}>
            {(() => {
              // Check if there are any missing prices for this lot
              const hasMissingPrices = effectiveSkuSizes.some(sku => {
                const gradeKey = sku.id;
                return !lot.grades?.[gradeKey]?.price;
              });
              
              // If there are missing prices AND amount is 0, show empty
              if (hasMissingPrices && (!lot.amount || lot.amount === 0)) {
                return '';
              }
              
              // Otherwise show the amount with currency
              return `₹${lot.amount || 0}`;
            })()}
          </td>
        </tr>
      );

      rows.push(
        <tr key={`lot-${index}-price`}>
          <td className='label-cell'>
            Price-
          </td>
          {/* Dynamic price data for each grade based on effectiveSkuSizes */}
          {effectiveSkuSizes.map((sku) => {
            // For SKU sizes from API, use the ID directly as the grade key
            // For fallback sizes (extracted from data), the ID is already the grade key
            const gradeKey = sku.id;
            
            return (
              <td key={`price-${sku.id}`} className='grade-data'>
                {lot.grades?.[gradeKey]?.price || ''}
              </td>
            );
          })}
        </tr>
      );

      rows.push(
        <tr key={`lot-${index}-loader`}>
          <td className='label-cell' colSpan={effectiveSkuSizes.length + 1} style={{ textAlign: 'left' }}>
            Loader- {lot?.loader || ''}
          </td>
        </tr>
      );
    });

    return rows;
  };

  return (
    <>
      <Container ref={ref}>
        <ContentWrapper>
          {/* Generated Timestamp */}
          <GeneratedTimestamp>
            Generated at: {new Date().toLocaleString('en-IN', {
              timeZone: 'Asia/Kolkata',
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            })}
          </GeneratedTimestamp>
          
          {/* Header Section */}
          <div>
          {/* Header Section: Date, Title, and Logo */}
          <HeaderSection>
            {/* Date Field */}
            <DateField>
              <DateBox>
                <DateText>
                  Date: {auctionDate ? getDate(auctionDate) : ''}
                </DateText>
              </DateBox>
            </DateField>

            {/* Title */}
            <TitleSection>
              <MainTitle>Token Slip/Price Card</MainTitle>
              <SubTitle>(टोकन पर्ची/मूल्य कार्ड)</SubTitle>
            </TitleSection>

            {/* Logo */}
            <LogoSection>
              {brandLogo ? (
                <BrandLogo src={brandLogo} alt='Brand Logo' />
              ) : (
                <>
                  <BrandText>
                    <FruitXText>FRUIT</FruitXText>
                    <XText>X</XText>
                  </BrandText>
                  <CompanyText>Chifu Agritech Pvt. Ltd.</CompanyText>
                </>
              )}
            </LogoSection>
          </HeaderSection>

          {/* Top Row of Information Boxes */}
          <InfoGrid>
            {/* Token No. */}
            <InfoBox>
              <InfoLabel>Token No. (टोकन संख्या)</InfoLabel>
              <InfoValue>{tokenNumber || ''}</InfoValue>
            </InfoBox>

            {/* Box/Truck */}
            <InfoBox>
              <InfoLabel>Box/Truck (डिब्बा/ट्रक)</InfoLabel>
              <InfoValue>Box</InfoValue>
            </InfoBox>

            {/* Boxes Brought */}
            <InfoBox>
              <InfoLabel>Boxes Brought (आवक डिब्बा)</InfoLabel>
              <InfoValue>{boxesBrought || ''}</InfoValue>
            </InfoBox>

            {/* Pack House */}
            <InfoBox>
              <InfoLabel>&nbsp;</InfoLabel>
              <InfoValue>{mandiName || ''}</InfoValue>
            </InfoBox>
          </InfoGrid>

          {/* Main Grid for Farmer and Transporter Details */}
          <DetailsGrid>
            {/* Left Column: Farmer Details */}
            <DetailsColumn>
              {/* Farmer Name */}
              <DetailRow>
                <DetailLabel>
                  Farmer Name
                  <br />
                  (किसान का नाम)
                </DetailLabel>
                <DetailValue>{farmerName || ''}</DetailValue>
              </DetailRow>

              {/* Mobile Number */}
              <DetailRow>
                <DetailLabel>
                  Mobile Number
                  <br />
                  (मोबाइल नंबर)
                </DetailLabel>
                <DetailValue>{mobileNumber || ''}</DetailValue>
              </DetailRow>

              {/* Address */}
              <DetailRow>
                <DetailLabel>Address (पता)</DetailLabel>
                <DetailValue>{address || ''}</DetailValue>
              </DetailRow>
            </DetailsColumn>

            {/* Right Column: Transporter Details */}
            <DetailsColumn>
              {/* Transporter/Driver */}
              <DetailRow>
                <DetailLabel>
                  Transporter/ Driver
                  <br />
                  (ट्रांसपोर्टर/ड्राइवर नाम)
                </DetailLabel>
                <DetailValue>{transporterName || ''}</DetailValue>
              </DetailRow>

              {/* Vehicle Number */}
              <DetailRow>
                <DetailLabel>
                  Vehicle Number
                  <br />
                  (गाडी नंबर)
                </DetailLabel>
                <DetailValue>{vehicleNumber || ''}</DetailValue>
              </DetailRow>

              {/* Transporter Payment */}
              <DetailRow>
                <DetailLabel>Transporter Payment</DetailLabel>
                <DetailValue>{hasTransporterPayment ? 'Yes' : 'No'}</DetailValue>
              </DetailRow>
            </DetailsColumn>
          </DetailsGrid>
        </div>

        {/* Main Lots Table */}
        <div className='main-table'>
          <div className='watermark'>FRUITX</div>
          <table className='lots-table'>
            <thead>
              <tr>
                <th rowSpan='2'>
                  Mandi
                  <br />
                  No.
                </th>
                <th rowSpan='2'>Variety</th>
                <th rowSpan='2'>Lot</th>
                <th></th>
                {/* Dynamic headers based on effectiveSkuSizes */}
                {effectiveSkuSizes.map((sku) => (
                  <th key={`header-${sku.id}`}>
                    {sku.size || ''}
                  </th>
                ))}
                <th rowSpan='2'>Amount</th>
              </tr>
            </thead>
            <tbody>{renderDataRows()}            </tbody>
            <tfoot>
              <tr className='footer-row'>
                <td colSpan='3'></td>
                <td colSpan={effectiveSkuSizes.length + 1} className='footer-label'>
                  Gross Amount
                </td>
                <td className='footer-value'>
                  {amountData?.totalAmount > 0 ? `₹${amountData.totalAmount}` : ''}
                </td>
              </tr>
              <tr className='footer-row'>
                <td colSpan='3'></td>
                <td colSpan={effectiveSkuSizes.length + 1} className='footer-label'>
                  Less Expenses
                </td>
                <td className='footer-value'>
                  {amountData?.totalAmount > 0 ? `₹${amountData.lessExpenses}` : ''}
                </td>
              </tr>
              <tr className='footer-row final-amount'>
                <td colSpan='3'></td>
                <td colSpan={effectiveSkuSizes.length + 1} className='footer-label'>
                  Final Amount
                </td>
                <td className='footer-value'>
                  {amountData?.totalAmount > 0 ? `₹${amountData.finalAmount}` : ''}
                </td>
              </tr>
            </tfoot>
          </table>
        </div>

        {/* Footer Section */}
        <div className='footer'>
          <SignaturesSection>
            {/* Box 1: Supervisor Sign */}
            <SignatureBox>
              <SignatureLabel>Supervisor Sign:</SignatureLabel>
              <SignatureLine></SignatureLine>
            </SignatureBox>

            {/* Box 2: Unloading Confirmation */}
            <SignatureBox>
              <SignatureLabel>
                Unloading confirmation
                <br />
                (Boxes & Sign.)
              </SignatureLabel>
              <SignatureLine></SignatureLine>
            </SignatureBox>
          </SignaturesSection>
        </div>
        <ContentSpacer />
        </ContentWrapper>
      </Container>
    </>
  );
});

PrintTokenSlipPriceCard.displayName = 'PrintTokenSlipPriceCard';

PrintTokenSlipPriceCard.propTypes = {
  data: PropTypes.array,
  auctionDate: PropTypes.string,
  brandLogo: PropTypes.string,
  mandiName: PropTypes.string,
  farmerName: PropTypes.string,
  transporterName: PropTypes.string,
  mobileNumber: PropTypes.string,
  vehicleNumber: PropTypes.string,
  address: PropTypes.string,
  tokenNumber: PropTypes.string,
  boxesBrought: PropTypes.string,
  amountData: PropTypes.shape({
    totalAmount: PropTypes.number,
    lessExpenses: PropTypes.number,
    finalAmount: PropTypes.number,
  }),
  skuSizes: PropTypes.array,
  hasTransporterPayment: PropTypes.bool,
};

export default PrintTokenSlipPriceCard;

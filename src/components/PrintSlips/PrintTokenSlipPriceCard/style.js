import styled from 'styled-components';

export const ContentWrapper = styled.div`
  display: flex;
  flex-direction: column;

  @media print {
    /* Just ensure content flows normally without forcing height */
    display: flex !important;
    flex-direction: column !important;
  }
`;

export const ContentSpacer = styled.div`
  height: 15px;
  margin-bottom: 10px;

  @media print {
    height: 5mm !important;
    margin-bottom: 3mm !important;
    flex-shrink: 0;
  }
`;

// Header Section Components
export const HeaderSection = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  margin-top: 0;

  @media print {
    margin-bottom: 3mm !important;
    margin-top: 0 !important;
  }
`;

export const DateField = styled.div`
  width: 25%;
`;

export const DateBox = styled.div`
  border: 1px solid black;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  height: 2.5rem;
`;

export const DateText = styled.span`
  font-weight: 700;
  margin-right: 0.5rem;
`;

export const TitleSection = styled.div`
  width: 50%;
  text-align: center;
`;

export const MainTitle = styled.h1`
  font-size: 1.875rem;
  font-weight: 700;
  margin: 0;
`;

export const SubTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
`;

export const LogoSection = styled.div`
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
`;

export const BrandLogo = styled.img`
  max-height: 60px;
  max-width: 120px;
  width: auto;
  height: auto;
  object-fit: contain;

  @media print {
    max-height: 15mm !important;
    max-width: 30mm !important;
    width: auto !important;
    height: auto !important;
    object-fit: contain !important;
  }
`;

export const BrandText = styled.div`
  font-weight: bold;
  font-size: 2.25rem;
  letter-spacing: -0.05em;
`;

export const FruitXText = styled.span`
  color: #4caf50;
`;

export const XText = styled.span`
  color: #f44336;
`;

export const CompanyText = styled.p`
  font-weight: 500;
  font-size: 1rem;
  margin: 0;
  margin-top: 4px;
  width: 100%;
  text-align: center;
`;

// Info Grid Components
export const InfoGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  gap: 0.8rem;
  margin-bottom: 0.8rem;
  align-items: end;

  @media print {
    gap: 2mm !important;
    margin-bottom: 3mm !important;
  }
`;

export const InfoBox = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
`;

export const InfoLabel = styled.div`
  text-align: center;
  font-weight: 500;
  margin-bottom: 0.25rem;
  min-height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem;
`;

export const InfoValue = styled.div`
  border: 1px solid black;
  padding: 0.5rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-sizing: border-box;
`;

// Details Grid Components
export const DetailsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  column-gap: 1.5rem;
  row-gap: 0.8rem;

  @media print {
    column-gap: 4mm !important;
    row-gap: 2mm !important;
  }
`;

export const DetailsColumn = styled.div`
  display: flex;
  flex-direction: column;
  row-gap: 0.8rem;

  @media print {
    row-gap: 2mm !important;
  }
`;

export const DetailRow = styled.div`
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  align-items: center;
`;

export const DetailLabel = styled.div`
  grid-column: span 1 / span 1;
  border: 1px solid black;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  height: 2.5rem;
  justify-content: center;
  text-align: center;
  font-weight: 500;
`;

export const DetailValue = styled.div`
  grid-column: span 2 / span 2;
  border: 1px solid black;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  height: 2.5rem;
`;

export const PaymentOptionValue = styled.div`
  grid-column: span 1 / span 1;
  border: 1px solid black;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  height: 2.5rem;
  justify-content: center;
  font-weight: 500;
`;

// Signature Section Components
export const SignaturesSection = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
  padding-top: 40px;
`;

export const SignatureBox = styled.div`
  /* No specific styles needed as parent handles alignment */
`;

export const SignatureLabel = styled.span`
  display: block;
  text-align: center;
  font-size: 14px;
  color: #333;
  margin-bottom: 25px;
  line-height: 1.4;

  /* Special handling for multi-line labels */
  &:has(br) {
    margin-bottom: 10px;
  }
`;

export const SignatureLine = styled.div`
  border-bottom: 1px solid #000;
  width: 250px;
`;

export const LabelCell = styled.td`
  text-align: left !important;
  padding: 2px 8px !important;
  font-size: 10px;
  background: transparent;
`;

export const Container = styled.div`
  /* Use printable width from the start - A4 width minus standard margins */
  width: 190mm;
  max-width: 190mm;
  margin: 0 auto;
  padding: 3mm;
  background: white;
  border: 1px solid black;
  font-family: Arial, sans-serif;
  font-size: 11px;
  line-height: 1.4;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow-x: auto; /* Allow horizontal scroll in preview if needed */

  @media print {
    /* Maximize width while preventing cutoff */
    width: 200mm !important;
    max-width: 200mm !important;
    margin: 5mm 5mm 5mm 5mm !important; /* top right bottom left - minimal left/right margins */
    padding: 3mm !important;
    border: 1px solid black !important;
    page-break-after: always;
    page-break-inside: avoid;
    box-sizing: border-box !important;
    overflow-x: hidden !important;

    /* Ensure colors print correctly */
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;

    /* Remove min-height so container fits content */
    min-height: auto !important;
    height: auto !important;

    /* Ensure content fits within printable area */
    max-width: 190mm !important;
    overflow: visible !important;

    /* Ensure each page starts on a new page */
    &:not(:first-child) {
      page-break-before: always;
    }

    /* Last page should not have page break after */
    &:last-child {
      page-break-after: auto;
    }
  }

  @page {
    margin: 10mm;
    size: A4;
  }

  body {
    font-family:
      'Inter', sans-serif; /* Fallback to sans-serif if Inter is not available */
    background-color: #f3f4f6;
    margin: 0;
    padding: 32px;
  }

  /* Header Section */
  .header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    padding-bottom: 10px;
  }

  .header-left {
    flex: 1;
    padding: 5px;
  }

  .header-center {
    flex: 2;
    text-align: center;
    padding: 5px;
  }

  .header-right {
    flex: 1;
    text-align: right;
    padding: 5px;
  }

  .token-info {
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 5px;
  }

  .token-number {
    font-size: 24px;
    font-weight: bold;
    color: red;
    margin: 10px 0;
  }

  .date-info {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
  }

  .logo {
    width: 80px;
    height: 60px;
    object-fit: contain;
    margin-bottom: 10px;
  }

  .title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .subtitle {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .company-name {
    font-size: 14px;
    font-weight: 600;
    margin-top: 10px;
  }

  .box-info {
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 5px;
  }

  .box-count {
    font-size: 18px;
    font-weight: bold;
    color: red;
    margin: 10px 0;
  }

  .pack-house {
    font-size: 12px;
    font-weight: 600;
    margin-top: 10px;
  }

  /* Farmer Details Section */
  .farmer-details {
    margin: 15px 0;
  }

  .details-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
  }

  .details-table td {
    border: 1px solid black;
    padding: 8px;
    font-size: 11px;
    vertical-align: top;
  }

  .details-table .label {
    background-color: #f5f5f5;
    font-weight: 600;
    width: 15%;
    text-align: center;
  }

  .details-table .value {
    font-weight: 700;
    font-size: 12px;
    color: #000;
    width: 35%;
  }

  /* Main Table */
  .main-table {
    margin: 15px 0;
    position: relative;
    background: transparent;
  }

  /* Watermark */
  .watermark {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 120px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.1);
    z-index: 1;
    pointer-events: none;
    user-select: none;
  }

  .lots-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid black;
    position: relative;
    z-index: 2;
    background: transparent;
    table-layout: auto;
  }

  .lots-table th,
  .lots-table td {
    border: 1px solid black;
    padding: 2px;
    text-align: center;
    vertical-align: middle;
    font-size: 10px; /* Increased from 8px */
    background: transparent;
    word-wrap: break-word;
  }

  .lots-table th {
    background-color: #f0f0f0;
    font-weight: bold;
    font-size: 11px; /* Increased from 9px */
    line-height: 1.2;
  }

  .mandi-no {
    font-weight: bold; /* Made bold */
    font-size: 10px; /* Increased from 8px */
    width: auto;
    min-width: 40px; /* Slightly increased */
  }

  .variety {
    font-size: 10px; /* Increased from 8px */
    width: auto;
    min-width: 35px; /* Reduced from 45px to give space to SKU columns */
    max-width: 35px;
  }

  .lot {
    font-size: 10px; /* Increased from 8px */
    width: auto;
    min-width: 35px;
  }

  .grade-data {
    font-size: 11px; /* Increased from 9px */
    width: auto;
    min-width: 35px; /* Increased from 30px */
    padding: 1px !important;
    font-weight: bold; /* Make quantities bold */
  }

  .label-cell {
    font-size: 10px !important;
    text-align: left !important;
    padding: 2px 8px !important;
    background: transparent !important;
  }

  .amount {
    font-weight: bold;
    font-size: 10px; /* Increased from 8px */
    width: auto;
    min-width: 50px;
    white-space: nowrap;
  }

  /* Mandi number with total units styling */
  .mandi-no-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    line-height: 1.1;
  }

  .mandi-number {
    font-weight: bold;
    font-size: 10px;
  }

  .total-units {
    font-weight: bold;
    font-size: 10px; /* Increased from 8px */
    color: #666;
    margin-top: 1px;
  }

  /* SKU header styling for two-line display */
  .sku-header {
    width: auto;
    min-width: 35px; /* Increased width for SKU columns */
    max-width: 45px;
    padding: 1px !important;
    line-height: 1.1;
  }

  .sku-line-1 {
    font-weight: bold;
    font-size: 9px;
    line-height: 1;
  }

  .sku-line-2 {
    font-weight: bold;
    font-size: 8px;
    line-height: 1;
    margin-top: 1px;
  }

  /* Bold styling for specific fields */
  .label-cell {
    font-weight: bold;
  }

  /* Make token number and vehicle number bold */
  .token-number,
  .vehicle-number {
    font-weight: bold !important;
  }

  /* Make QTY label bold */
  .qty-label {
    font-weight: bold !important;
  }

  /* Token Number - Bigger within InfoValue */
  .token-number-big {
    font-size: 20px !important;
    font-weight: bold !important;
    color: #d32f2f !important;
    border: 2px solid black !important;
    height: 3rem !important;
    padding: 0.5rem !important;
  }

  .empty-row {
    height: 25px;
  }

  /* Price-Loader Row Styles */
  .price-loader-row {
    height: 20px;
  }

  .price-loader-cell {
    text-align: left !important;
    padding: 2px 8px !important;
    font-size: 8px;
    background: transparent;
  }

  .price-loader-content {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }

  .price-loader-content span {
    font-weight: 500;
    color: #666;
  }

  /* Footer Styles */
  .lots-table tfoot {
    background-color: #f9f9f9;
  }

  .footer-row td {
    border: 1px solid black;
    padding: 8px;
    font-size: 11px;
  }

  .footer-label {
    text-align: right !important;
    font-weight: 600;
    background-color: #f5f5f5;
  }

  .footer-value {
    font-weight: bold;
    text-align: center !important;
    background-color: white;
    white-space: nowrap;
  }

  .final-amount {
    background-color: #e0e0e0;
  }

  .final-amount td {
    font-weight: bold;
    background-color: #e0e0e0;
  }

  .final-amount .footer-value {
    background-color: #e0e0e0;
    white-space: nowrap;
  }

  /* Footer Section */
  .footer {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding-top: 15px;
  }

  .totals-section {
    flex: 0 0 200px;
  }

  .totals-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid black;
  }

  .totals-table td {
    border: 1px solid black;
    padding: 8px;
    font-size: 12px;
  }

  .totals-table .label {
    font-weight: 600;
    background-color: #f5f5f5;
    width: 60%;
  }

  .totals-table .value {
    font-weight: bold;
    text-align: right;
  }

  .signatures-section {
    flex: 1;
    margin: 0 20px;
    text-align: center;
  }

  .signature-box {
    margin-bottom: 20px;
  }

  .signature-label {
    font-size: 10px;
    font-weight: 600;
    margin-bottom: 40px;
  }

  .signature-line {
    border-bottom: 1px solid black;
    margin-bottom: 10px;
    padding-bottom: 5px;
  }

  .payment-section {
    flex: 0 0 150px;
    text-align: center;
  }

  .payment-label {
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 12px;
  }

  .payment-options {
    display: flex;
    justify-content: space-around;
    margin-top: 10px;
  }

  .payment-options span {
    font-size: 12px;
    font-weight: 600;
  }
`;

// Generated timestamp component
export const GeneratedTimestamp = styled.div`
  font-size: 10px;
  color: #666;
  text-align: right;
  margin-bottom: 3px;
  margin-top: 0;

  @media print {
    font-size: 9px !important;
    color: #333 !important;
    margin-bottom: 2mm !important;
    margin-top: 0 !important;
  }
`;

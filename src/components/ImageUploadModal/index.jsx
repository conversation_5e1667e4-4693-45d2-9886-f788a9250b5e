import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Box,
  Typography,
  Button,
  Tab,
  Tabs,
  IconButton,
  Grid,
  Card,
  CardMedia,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  CloudUpload,
  CameraAlt,
  Delete,
  Close,
  Visibility,
  Add,
  CancelOutlined as CancelOutlinedIcon,
} from '@mui/icons-material';
import PropTypes from 'prop-types';
import { Webcam as WebcamCapture } from 'Components';
import { UploadInput } from 'Components/FormFields';

const ImageUploadModal = ({
  open,
  onClose,
  title = 'Upload Images',
  images = [],
  onImagesChange,
  saveImages,
  removeAttachments,
  setFieldValue,
  handleUpload,
  upload,
  setPhotos,
  maxImages = 10,
  acceptedFormats = 'image/*',
  webcamConstraints = { height: '300', width: '300' },
  webcamStyle = { height: '300', width: '300' },
  showMandatoryMessage = false,
  mandatoryMessage = '',
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [previewImage, setPreviewImage] = useState(null);
  const [previewOpen, setPreviewOpen] = useState(false);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleImagePreview = image => {
    setPreviewImage(image);
    setPreviewOpen(true);
  };

  const handleImageDelete = imageIndex => {
    // Always update the local state first
    const updatedImages = images.filter((_, index) => index !== imageIndex);

    // Update the parent component through onImagesChange (this is the primary way)
    if (onImagesChange) {
      onImagesChange(updatedImages);
    }

    // Try to call removeAttachments only if it exists and looks safe to call
    if (removeAttachments && typeof removeAttachments === 'function') {
      try {
        // Check if this is the complex removeAttachments that needs multiple params
        // If so, we'll skip it since we don't have all the required params
        const funcString = removeAttachments.toString();
        if (funcString.includes('setFieldValue') && !setFieldValue) {
          console.warn(
            'Skipping removeAttachments call - setFieldValue not available'
          );
          return;
        }
        removeAttachments(imageIndex);
      } catch (error) {
        console.warn('removeAttachments failed:', error);
        // Continue - the image is already removed from local state
      }
    }
  };

  const handleClosePreview = () => {
    setPreviewOpen(false);
    setPreviewImage(null);
  };

  const TabPanel = ({ children, value, index, ...other }) => {
    return (
      <div
        role='tabpanel'
        hidden={value !== index}
        id={`image-tabpanel-${index}`}
        aria-labelledby={`image-tab-${index}`}
        {...other}
      >
        {value === index && <Box sx={{ py: 2 }}>{children}</Box>}
      </div>
    );
  };

  return (
    <>
      <Modal
        open={open}
        onClose={onClose}
        aria-labelledby='image-upload-modal-title'
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Box
          sx={{
            width: '95%',
            maxWidth: 1200,
            maxHeight: '95vh',
            bgcolor: 'background.paper',
            borderRadius: 2,
            boxShadow: 24,
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          {/* Header */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              p: 2,
              borderBottom: '1px solid',
              borderColor: 'divider',
            }}
          >
            <Typography variant='h6' component='h2'>
              {title}
            </Typography>
            <IconButton onClick={onClose} size='small'>
              <Close />
            </IconButton>
          </Box>

          {/* Content */}
          <Box sx={{ flex: 1, overflow: 'auto' }}>
            {/* Tabs Section */}
            <Box sx={{ p: 2 }}>
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                aria-label='image upload tabs'
                sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
              >
                <Tab
                  icon={<CloudUpload />}
                  label='Upload Images'
                  id='image-tab-0'
                  aria-controls='image-tabpanel-0'
                />
                <Tab
                  icon={<CameraAlt />}
                  label='Capture with Camera'
                  id='image-tab-1'
                  aria-controls='image-tabpanel-1'
                />
              </Tabs>

              {/* Upload Tab */}
              <TabPanel value={activeTab} index={0}>
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <input
                    type='file'
                    accept={acceptedFormats}
                    multiple
                    style={{ display: 'none' }}
                    id='image-upload-input'
                    onChange={event => {
                      const files = Array.from(event.target.files);
                      if (onImagesChange && files.length > 0) {
                        onImagesChange([...images, ...files]);
                      }
                    }}
                  />
                  <label htmlFor='image-upload-input'>
                    <Button
                      variant='contained'
                      component='span'
                      startIcon={<CloudUpload />}
                      size='large'
                      sx={{
                        minWidth: 200,
                        py: 2,
                        fontSize: '1.1rem',
                        fontWeight: 600,
                      }}
                    >
                      Choose Images to Upload
                    </Button>
                  </label>
                  <Typography
                    variant='body2'
                    color='text.secondary'
                    sx={{ mt: 2 }}
                  >
                    Select multiple images at once
                  </Typography>
                  {showMandatoryMessage && mandatoryMessage && (
                    <Typography variant='body2' color='error' sx={{ mt: 1 }}>
                      {mandatoryMessage}
                    </Typography>
                  )}
                </Box>
              </TabPanel>

              {/* Camera Tab */}
              <TabPanel value={activeTab} index={1}>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    py: 3,
                    minHeight: 400,
                  }}
                >
                  <Typography variant='h6' sx={{ mb: 2, textAlign: 'center' }}>
                    Capture Image with Camera
                  </Typography>
                  <Box
                    sx={{
                      border: '2px dashed #ccc',
                      borderRadius: 2,
                      p: 2,
                      backgroundColor: '#fafafa',
                    }}
                  >
                    <WebcamCapture
                      saveImages={capturedImage => {
                        // Add captured image to the modal's image state
                        if (onImagesChange) {
                          onImagesChange([...images, capturedImage]);
                        }
                        // Call original saveImages if provided
                        if (saveImages) {
                          saveImages(capturedImage);
                        }
                      }}
                      removeAttachments={removeAttachments}
                      setFieldValue={setFieldValue}
                      Webstyle={{
                        height: '400px',
                        width: '500px',
                        borderRadius: '8px',
                        ...webcamStyle,
                      }}
                      constraints={{
                        height: '400',
                        width: '500',
                        ...webcamConstraints,
                      }}
                      setPhotos={photos => {
                        // Update modal's image state when photos change
                        if (onImagesChange && Array.isArray(photos)) {
                          onImagesChange(photos);
                        }
                        // Call original setPhotos if provided
                        if (setPhotos) {
                          setPhotos(photos);
                        }
                      }}
                      handleUpload={() => {
                        // Handle upload functionality if needed
                        if (handleUpload) {
                          handleUpload();
                        }
                      }}
                      upload={true}
                      showImagePreview={false}
                      showCloseIcon={false}
                    />
                  </Box>
                  {showMandatoryMessage && mandatoryMessage && (
                    <Typography
                      variant='body2'
                      color='error'
                      sx={{ mt: 2, textAlign: 'center' }}
                    >
                      {mandatoryMessage}
                    </Typography>
                  )}
                </Box>
              </TabPanel>
            </Box>

            {/* Image Preview Section */}
            {images && images.length > 0 && (
              <Box
                sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider' }}
              >
                <Typography
                  variant='subtitle1'
                  sx={{ mb: 2, fontWeight: 'bold' }}
                >
                  Uploaded Images ({images.length})
                </Typography>
                <Grid container spacing={2}>
                  {images.map((image, index) => (
                    <Grid item xs={6} sm={4} md={3} key={index}>
                      <Card sx={{ position: 'relative' }}>
                        <CardMedia
                          component='img'
                          height='120'
                          image={
                            typeof image === 'string'
                              ? image
                              : image?.url
                                ? image.url
                                : URL.createObjectURL(image)
                          }
                          alt={`Uploaded image ${index + 1}`}
                          sx={{ cursor: 'pointer' }}
                          onClick={() => handleImagePreview(image)}
                        />
                        <CardActions
                          sx={{
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            p: 0.5,
                          }}
                        >
                          <CancelOutlinedIcon
                            className='cancel-icon'
                            color='error'
                            cursor='pointer'
                            style={{
                              position: 'absolute',
                              top: '0',
                              right: '0',
                            }}
                            onClick={() => handleImageDelete(index)}
                            data-cy='mandi.webCam.close'
                          />
                        </CardActions>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}
          </Box>

          {/* Footer */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              p: 2,
              borderTop: '1px solid',
              borderColor: 'divider',
              bgcolor: 'grey.50',
            }}
          >
            <Typography variant='body2' color='text.secondary'>
              {images.length} / {maxImages} images uploaded
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button variant='outlined' onClick={onClose}>
                Close
              </Button>
            </Box>
          </Box>
        </Box>
      </Modal>

      {/* Image Preview Dialog */}
      <Dialog
        open={previewOpen}
        onClose={handleClosePreview}
        maxWidth='md'
        fullWidth
      >
        <DialogTitle>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            Image Preview
            <IconButton onClick={handleClosePreview} size='small'>
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          {previewImage && (
            <Box sx={{ textAlign: 'center' }}>
              <img
                src={
                  typeof previewImage === 'string'
                    ? previewImage
                    : previewImage?.url
                      ? previewImage.url
                      : URL.createObjectURL(previewImage)
                }
                alt='Preview'
                style={{
                  maxWidth: '100%',
                  maxHeight: '70vh',
                  objectFit: 'contain',
                }}
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePreview}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

// Trigger Button Component
export const ImageUploadButton = ({
  title = 'Upload Images',
  variant = 'contained',
  color = 'primary',
  size = 'medium',
  startIcon = <Add />,
  children,
  sx = {},
  ...modalProps
}) => {
  const [modalOpen, setModalOpen] = useState(false);

  const handleOpenModal = () => {
    setModalOpen(true);
  };

  const handleCloseModal = () => {
    setModalOpen(false);
  };

  return (
    <>
      <Button
        variant={variant}
        color={color}
        size={size}
        startIcon={startIcon}
        onClick={handleOpenModal}
        sx={sx}
      >
        {children || title}
      </Button>

      <ImageUploadModal
        {...modalProps}
        title={title}
        open={modalOpen}
        onClose={handleCloseModal}
      />
    </>
  );
};

ImageUploadModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string,
  images: PropTypes.array,
  onImagesChange: PropTypes.func,
  saveImages: PropTypes.func,
  removeAttachments: PropTypes.func,
  setFieldValue: PropTypes.func,
  handleUpload: PropTypes.func,
  upload: PropTypes.bool,
  setPhotos: PropTypes.func,
  maxImages: PropTypes.number,
  acceptedFormats: PropTypes.string,
  webcamConstraints: PropTypes.object,
  webcamStyle: PropTypes.object,
  showMandatoryMessage: PropTypes.bool,
  mandatoryMessage: PropTypes.string,
};

ImageUploadButton.propTypes = {
  title: PropTypes.string,
  variant: PropTypes.string,
  color: PropTypes.string,
  size: PropTypes.string,
  startIcon: PropTypes.element,
  children: PropTypes.node,
  sx: PropTypes.object,
};

export default ImageUploadModal;

import { useEffect, useState } from 'react';

import {
  Checkbox,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableHead,
  TablePagination,
  TableRow,
  Typography,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { makeStyles } from '@mui/styles';
import clsx from 'clsx';
import _get from 'lodash/get';
import _omit from 'lodash/omit';

const useStyles = makeStyles({
  root: {
    width: '100%',
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    overflow: 'auto',
  },
  container: {
    flex: 1,
  },
  rejectReasonText: {
    color: 'red',
  },
  rejectReasonWrapper: {
    backgroundColor: 'lightGrey',
    boxShadow: '0 5px 6px -6px grey',
  },
  tableBorderColor: {
    border: '1px solid red',
  },

  preventHorizontalScrollTable: {
    '@media (max-width: 409px)': {
      '& .MuiTableCell-root': {
        padding: '4px 8px !important',
        fontSize: '0.75rem !important',
        lineHeight: '1.2 !important',
      },
      '& .MuiTableCell-head': {
        fontSize: '0.7rem',
        fontWeight: '700', // Extra bold for mobile
      },
      '& .MuiTypography-caption': {
        fontSize: '0.65rem',
      },
      '& .MuiCheckbox-root': {
        padding: '2px',
        '& .MuiSvgIcon-root': {
          fontSize: '1rem',
        },
      },
    },
  },
});

const StyledTableRow = styled(TableRow, {
  shouldForwardProp: prop => prop !== 'borderColor' && prop !== 'isEqual',
})(({ theme, borderColor, isEqual }) => ({
  boxShadow: borderColor && isEqual ? '0 1px 0 0 red, 0 -1px 0 0 red' : 'none',
  '&:nth-of-type(odd)': {
    backgroundColor: theme.palette.action.hover,
  },
  position: 'relative',
}));

const RejectReasonRow = ({
  colSpan,
  reject_reason,
  preventHorizontalScroll,
}) => {
  const { rejectReasonText, rejectReasonWrapper } = useStyles();
  return (
    <TableRow className={rejectReasonWrapper}>
      <TableCell colSpan={colSpan}>
        <Typography
          component='span'
          color='red'
          className={rejectReasonText}
          style={
            preventHorizontalScroll && window.innerWidth <= 409
              ? { fontSize: '0.7rem' }
              : {}
          }
        >
          <b>Reject Reason:</b>
        </Typography>
        <Typography
          component='span'
          style={
            preventHorizontalScroll && window.innerWidth <= 409
              ? { fontSize: '0.7rem' }
              : {}
          }
        >
          {reject_reason}
        </Typography>
      </TableCell>
    </TableRow>
  );
};

export const TableHeader = ({
  columns,
  totalRows,
  numSelected,
  onSelectAll,
  isSelection,
  preventHorizontalScroll,
}) => (
  <TableHead>
    <TableRow>
      {isSelection && (
        <TableCell padding='checkbox'>
          {onSelectAll && (
            <Checkbox
              color='primary'
              indeterminate={numSelected > 0 && numSelected < totalRows}
              checked={totalRows > 0 && numSelected === totalRows}
              onChange={onSelectAll}
              inputProps={{ 'aria-label': 'Select All' }}
            />
          )}
        </TableCell>
      )}
      {columns.map(({ key, header, subHeader, ...restProps }) => (
        <TableCell variant='head' key={key} {..._omit(restProps, 'render')}>
          <Typography
            variant='subtitle2'
            sx={{ fontWeight: 'bold', fontSize: '0.875rem' }}
          >
            {header}
          </Typography>
          {subHeader && (
            <Typography
              variant='caption'
              component='div'
              className='disabled-text'
              color='textPrimary'
            >
              {subHeader}
            </Typography>
          )}
        </TableCell>
      ))}
    </TableRow>
  </TableHead>
);

export const TableFooterRow = ({ columns, footerSummarydata }) => (
  <TableFooter>
    <TableRow>
      {columns.map(({ key, footer, ...restProps }) => (
        <TableCell variant='head' key={key} {..._omit(restProps, 'render')}>
          <b>
            {typeof footerSummarydata[footer] !== 'undefined'
              ? footerSummarydata[footer]
              : footer}
          </b>
        </TableCell>
      ))}
    </TableRow>
  </TableFooter>
);

export const TableBodyRow = ({
  columns,
  rowData,
  rowIndex,
  rowProps,
  cellProps,
  onSelect,
  isSelection,
  selected,
  dataKey,
  borderColor,
  onclickFunction = () => {},
  preventHorizontalScroll,
}) => {
  const rowProp = rowProps ? rowProps(rowData) : {};
  const { tableBorderColor } = useStyles();

  const isEqual =
    rowData.units === (rowData.selling_price || rowData.selling_price_per_unit);
  return (
    <StyledTableRow
      {...rowProp}
      borderColor={borderColor}
      isEqual={isEqual}
      className={borderColor && isEqual ? tableBorderColor : {}}
    >
      {isSelection && (
        <TableCell padding='checkbox'>
          {onSelect && (
            <Checkbox
              color='primary'
              value={_get(rowData, dataKey)}
              onChange={onSelect}
              checked={selected?.includes(_get(rowData, dataKey))}
              inputProps={{ 'aria-labelledby': 'Select' }}
            />
          )}
        </TableCell>
      )}
      {columns.map(({ key, render, ...restProps }, index) => {
        const cellData = _get(rowData, key);
        const cellProp = cellProps
          ? cellProps({ data: cellData, key, rowData })
          : {};
        return (
          <TableCell
            key={key}
            {...restProps}
            onClick={e => onclickFunction(e, rowData.id)}
          >
            {render
              ? render({
                  data: cellData,
                  props: cellProp,
                  rowData,
                  index: { columnIndex: index, rowIndex },
                })
              : cellData}
          </TableCell>
        );
      })}
    </StyledTableRow>
  );
};

const CustomTable = ({
  header,
  columns,
  size,
  data,
  dataKey,
  sticky,
  rowProps,
  cellProps,
  rowsPerPage,
  totalRows,
  onSelectAll,
  onSelect,
  selected,
  currentPage,
  onChangePage,
  isSelection,
  className,
  isFooter,
  footerSummarydata,
  elevation = 1,
  onclickFunction,
  borderColor,
  showRejectReason = true,
  preventHorizontalScroll = false,
  ...props
}) => {
  const [tableData, setTableData] = useState(data);
  const classes = useStyles();

  useEffect(() => {
    setTableData(data);
  }, [data]);

  return (
    <Paper
      className={clsx(
        classes.root,
        className,
        preventHorizontalScroll && classes.preventHorizontalScrollTable
      )}
      elevation={elevation}
    >
      <TableContainer className={classes.container}>
        <Table size={size} stickyHeader={sticky} {...props}>
          {header && (
            <TableHeader
              columns={columns}
              totalRows={totalRows}
              numSelected={selected?.length || 0}
              onSelectAll={onSelectAll}
              isSelection={isSelection}
              preventHorizontalScroll={preventHorizontalScroll}
            />
          )}
          <TableBody>
            {tableData.map((row, index) => (
              <>
                <TableBodyRow
                  columns={columns}
                  key={_get(row, dataKey)}
                  dataKey={dataKey}
                  rowData={row}
                  rowIndex={index}
                  rowProps={rowProps}
                  cellProps={cellProps}
                  selected={selected}
                  onSelect={onSelect}
                  isSelection={isSelection}
                  onclickFunction={onclickFunction}
                  borderColor={borderColor}
                  preventHorizontalScroll={preventHorizontalScroll}
                />
                {showRejectReason && row.reject_reason && (
                  <RejectReasonRow
                    colSpan={columns.length}
                    reject_reason={row.reject_reason}
                    preventHorizontalScroll={preventHorizontalScroll}
                  />
                )}
              </>
            ))}
          </TableBody>
          {isFooter && (
            <TableFooterRow
              columns={columns}
              footerSummarydata={footerSummarydata}
            />
          )}
        </Table>
      </TableContainer>
      {totalRows > 0 && onChangePage && (
        <TablePagination
          rowsPerPageOptions={[]}
          component='div'
          count={totalRows}
          rowsPerPage={rowsPerPage}
          page={currentPage}
          onPageChange={onChangePage}
        />
      )}
    </Paper>
  );
};

CustomTable.defaultProps = {
  columns: [],
  data: [],
  dataKey: 'id',
  isSelection: false,
  rowsPerPage: 25,
  header: true,
  isFooter: false,
  footerSummarydata: [],
  cellProps: false,
  rowProps: false,
  sticky: false,
  size: 'small',
  preventHorizontalScroll: false,
};

export const TableElements = Table;

export default CustomTable;

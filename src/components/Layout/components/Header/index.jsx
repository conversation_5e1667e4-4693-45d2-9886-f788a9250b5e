import { useState } from 'react';

import {
  AccountCircle,
  LogoutOutlined,
  Menu as MenuIcon,
  KeyboardArrowDown as ArrowDownIcon,
} from '@mui/icons-material';
import {
  Box,
  Button,
  IconButton,
  ListItemIcon,
  ListItemText,
  MenuItem,
} from '@mui/material';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';

import { useSiteValue } from 'App/SiteContext';
import FruitXLogo from 'Assets/FruitX.png';
import { useAuth } from 'Contexts/AuthContext';

import { StyledAppBar, StyledMenu, StyledToolbar } from './styled';

const Header = ({ toggleSidebar }) => {
  const { userInfo, mandiList, mandiId, setMandiId, setDCId } = useSiteValue();
  const { logout } = useAuth();
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState(null);
  const [mandiAnchorEl, setMandiAnchorEl] = useState(null);

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMandiClose = () => {
    setMandiAnchorEl(null);
  };

  const selectMandi = (id, dc_id) => {
    setMandiId(id);
    setDCId(dc_id);
    handleMandiClose();
    // Auto-refresh the page after mandi change
    window.location.reload();
  };

  return (
    <StyledAppBar position='relative'>
      <StyledToolbar>
        <IconButton
          edge='start'
          color='inherit'
          aria-label='open sidebar'
          onClick={toggleSidebar}
          data-cy='mandi.header.openSidebar'
        >
          <MenuIcon />
        </IconButton>
        <img src={FruitXLogo} alt='logo' width='100' />
        <Box flex={1} />

        {/* Mandi Title in Center */}
        <Box
          sx={{
            position: 'absolute',
            left: '50%',
            transform: 'translateX(-50%)',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <Button
            variant='text'
            onClick={event => setMandiAnchorEl(event.currentTarget)}
            endIcon={<ArrowDownIcon sx={{ fontSize: '1rem' }} />}
            sx={{
              color: 'inherit',
              textTransform: 'none',
              fontWeight: 'bold',
              fontSize: '1rem', // Reduced from 1.25rem
              minWidth: 'auto',
              padding: '4px 8px',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
              },
              cursor: 'pointer',
              '& .MuiButton-endIcon': {
                marginLeft: '4px',
              },
            }}
          >
            {(mandiList || []).find(({ id }) => id === parseInt(mandiId, 10))
              ?.name || 'Select Mandi'}
          </Button>
        </Box>

        <Box flex={1} />
        <IconButton
          size='large'
          color='inherit'
          aria-label='profile icon'
          aria-controls='simple-menu'
          aria-haspopup='true'
          data-cy='mandi.profileIcon'
          onClick={event => setAnchorEl(event.currentTarget)}
        >
          <AccountCircle fontSize='inherit' />
        </IconButton>

        <StyledMenu
          id='user-menu'
          anchorEl={anchorEl}
          keepMounted
          open={!!anchorEl}
          onClose={handleClose}
        >
          <MenuItem>
            <ListItemIcon>
              <AccountCircle />
            </ListItemIcon>
            <ListItemText data-cy='mandi.username'>
              {userInfo?.name}
            </ListItemText>
          </MenuItem>
          <MenuItem data-cy='mandi.logout' onClick={logout}>
            <ListItemIcon>
              <LogoutOutlined />
            </ListItemIcon>
            Logout
          </MenuItem>
        </StyledMenu>

        {/* Mandi Selection Menu */}
        <StyledMenu
          id='mandi-menu'
          anchorEl={mandiAnchorEl}
          keepMounted
          open={!!mandiAnchorEl}
          onClose={handleMandiClose}
        >
          {!mandiList?.length ? (
            <MenuItem disabled>
              <ListItemText data-cy='mandi.selectMandi'>
                Select Mandi
              </ListItemText>
            </MenuItem>
          ) : (
            (mandiList || []).map(({ id, name, dc_id }) => (
              <MenuItem
                key={id}
                onClick={() => selectMandi(id, dc_id)}
                selected={id === parseInt(mandiId, 10)}
              >
                {name}
              </MenuItem>
            ))
          )}
        </StyledMenu>
      </StyledToolbar>
    </StyledAppBar>
  );
};

Header.propTypes = {
  toggleSidebar: PropTypes.func.isRequired,
};

export default Header;

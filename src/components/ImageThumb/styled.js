import styled from 'styled-components';

export const ImageWrapper = styled.span`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  margin-right: ${props => props.theme.spacing(1)};
  padding: ${props => props.theme.spacing(1)};
  background: ${props => props.theme.palette.grey[200]};
  border-radius: 4px;

  img {
    max-width: 100%;
    max-height: 230px;
    width: auto;
    height: auto;
    min-width: 100px;
    object-fit: contain;
    cursor: pointer;
  }

  .cancel-icon {
    position: absolute;
    top: -2px;
    right: -5px;
  }

  .pdf-icon {
    font-size: ${props => props.theme.typography.h1.fontSize};
    cursor: pointer;
  }
`;

export default ImageWrapper;

import React from 'react';

import { CssBaseline, LinearProgress } from '@mui/material';
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { GoogleOAuthProvider } from '@react-oauth/google';
import { SnackbarProvider } from 'notistack';
import {
  Navigate,
  Route,
  BrowserRouter as Router,
  Routes,
} from 'react-router-dom';
import { ThemeProvider } from 'styled-components';

import { SiteLayout } from 'Components/Layout/index.jsx';
import { AuthProvider } from 'Contexts/AuthContext';
import TabComponentsMap from 'Pages/index.jsx';

import theme from '../theme';

import App from './App';

// Google OAuth client ID - replace with your actual client ID from Google Developer Console
const GOOGLE_CLIENT_ID =
  import.meta.env.VITE_GOOGLE_CLIENT_ID ||
  '123456789-example.apps.googleusercontent.com';

const RenderApp = () => (
  <Router>
    <SiteLayout data-testid='app'>
      <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID}>
        <MuiThemeProvider theme={theme}>
          <ThemeProvider theme={theme}>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <CssBaseline />
              <SnackbarProvider autoHideDuration={3000} maxSnack={5}>
                <AuthProvider>
                  <React.Suspense fallback={<LinearProgress />}>
                    <Routes>
                      <Route
                        path='/login'
                        element={TabComponentsMap.LOGIN_PAGE}
                      />
                      <Route path='app/*' element={<App />} />
                      <Route
                        path='*'
                        element={
                          <Navigate
                            to='/login'
                            replace
                            element={TabComponentsMap.LOGIN_PAGE}
                          />
                        }
                      />
                    </Routes>
                  </React.Suspense>
                </AuthProvider>
              </SnackbarProvider>
            </LocalizationProvider>
          </ThemeProvider>
        </MuiThemeProvider>
      </GoogleOAuthProvider>
    </SiteLayout>
  </Router>
);

export default RenderApp;

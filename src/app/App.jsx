import { Routes, Route, Navigate, useNavigate } from 'react-router-dom';

import './App.css';
import AppLoader from 'Components/AppLoader';
import AuthGuard from 'Components/auth/AuthGuard';
import AppLayout from 'Components/Layout';
import TabComponentsMap from 'Pages/index.jsx';
import { getMandis } from 'Services/common';
import { userLogout, userPermission } from 'Services/users';
import { USER_PERMISSION } from 'Utilities/constants/userPermission';
import { processPermission } from 'Utilities/index.js';
import {
  getSavedMandiConfig,
  getSavedMandiId,
  getSavedUserDCId,
  getUserData,
  removeUser,
  saveAuctionDate,
  saveMandiConfig,
  saveMandiId,
  saveMandiList,
  saveUserDCId,
  getSavedPermissions,
  saveUserPermission,
} from 'Utilities/localStorage';

import React, { useState, useEffect } from 'react';

import { SiteProvider } from './SiteContext';

function App() {
  const navigate = useNavigate();

  const [dcId, setDCId] = useState(getSavedUserDCId());
  const [mandiId, setMandiId] = useState(getSavedMandiId());
  const [auctionDate, setAuctionDate] = useState(
    new Date().setHours(0, 0, 0, 0)
  );
  const [mandiConfig, setMandiConfig] = useState(getSavedMandiConfig());

  const [error, setError] = useState(false);
  const [errorInfo, setErrorInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [mandiList, setMandiList] = useState([]);
  const [permissions, setPermissions] = useState({});
  const [allowedTabs, setAllowedTabs] = useState([]);
  const userInfo = getUserData();

  const routeToLoginPage = () => {
    userLogout();
    removeUser();
    navigate('/login');
  };

  useEffect(() => {
    if (auctionDate !== null) {
      saveAuctionDate(auctionDate);
    }
  }, [auctionDate]);

  useEffect(() => {
    if (dcId !== null) {
      saveUserDCId(dcId);
    }
  }, [mandiId]);

  useEffect(() => {
    if (mandiId !== null) {
      saveMandiId(mandiId);
    }
  }, [mandiId]);

  useEffect(() => {
    if (mandiConfig !== null) {
      saveMandiConfig(mandiConfig);
    }
  }, [mandiConfig]);

  useEffect(() => {
    const fetchData = async () => {
      const savedPermission = getSavedPermissions();
      const allowedTabs = USER_PERMISSION() || [];
      setAllowedTabs(allowedTabs);
      setAuctionDate(auctionDate);

      try {
        const { items } = await getMandis({
          user_id: userInfo?.id,
          pack_auction_mandi: true,
        });
        saveMandiList(items);
        if (items?.[0]?.id) {
          setMandiId(prevMandiId => prevMandiId || items[0].id);
          setDCId(prevDCId => prevDCId || items[0].dc_id);
        }
        setMandiList(items);

        if (!savedPermission || savedPermission.length === 0) {
          const res = await userPermission();
          // Not using this configuration, so commenting out.
          saveUserPermission(res.permissions);
          setPermissions(processPermission(res.permissions));
        } else {
          setPermissions(processPermission(savedPermission));
        }

        setLoading(false);
      } catch (error) {
        setError(true);
        setErrorInfo(error);
      }
    };

    if (userInfo?.id) {
      fetchData();
    }
  }, [mandiId, auctionDate, userInfo]);

  if (error) return <div>Something went wrong. {errorInfo}</div>;

  if (loading) {
    return <AppLoader />;
  }

  return (
    <SiteProvider
      value={{
        userInfo,
        dcId,
        mandiList,
        mandiConfig,
        mandiId,
        auctionDate,
        setDCId,
        setMandiId,
        setAuctionDate,
        setMandiConfig,
        allowedTabs,
        logoutUser: routeToLoginPage,
      }}
    >
      <AppLayout>
        <Routes>
          {allowedTabs.map(t =>
            t.children
              ? [
                  ...t.children.map(child => {
                    const Component = TabComponentsMap[child.id];
                    return (
                      <Route
                        key={child.id}
                        path={`${t.url}/${child.url}/*`}
                        element={
                          <AuthGuard>
                            <React.Suspense fallback={<AppLoader />}>
                              <Component />
                            </React.Suspense>
                          </AuthGuard>
                        }
                      />
                    );
                  }),
                  (() => {
                    const Component = TabComponentsMap[t.id];
                    return (
                      <Route
                        key={t.id}
                        path={`${t.url}/*`}
                        element={
                          <AuthGuard>
                            <React.Suspense fallback={<AppLoader />}>
                              <Component />
                            </React.Suspense>
                          </AuthGuard>
                        }
                      />
                    );
                  })(),
                ]
              : (() => {
                  const Component = TabComponentsMap[t.id];
                  return (
                    <Route
                      key={t.id}
                      path={`${t.url}/*`}
                      element={
                        <AuthGuard>
                          <React.Suspense fallback={<AppLoader />}>
                            <Component />
                          </React.Suspense>
                        </AuthGuard>
                      }
                    />
                  );
                })()
          )}
          <Route path='*' element={<Navigate to='/app/home' replace />} />
        </Routes>
      </AppLayout>
    </SiteProvider>
  );
}

export default App;

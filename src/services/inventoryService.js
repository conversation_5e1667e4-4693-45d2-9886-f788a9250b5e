import { mandiService } from './base';

/**
 * Fetch inventory data based on filter parameters
 * @param {Object} params - Query parameters
 * @param {string} params.token - Optional token filter
 * @param {string} params.farmer_id - Optional farmer ID filter
 * @param {string} params.customer_id - Optional customer ID filter
 * @param {string} params.mandi_number_id - Optional mandi number ID filter
 * @param {string} params.loading_status - Optional status filter (YET_TO_LOAD, PARTIALLY_LOADED, FULLY_LOADED)
 * @returns {Promise} - API response
 */
export const fetchInventoryData = async (params = {}) => {
  try {
    const response = await mandiService.get('/inventory', {
      params,
    });

    if (response.responseData && response.responseData.mandi_numbers) {
      return {
        items: response.responseData.mandi_numbers,
        total: response.responseData.total || 0
      };
    }
    return { items: [], total: 0 };
  } catch (error) {
    console.error('Error fetching inventory data:', error);
    throw error;
  }
};

/**
 * Dispatch inventory with transportation details
 * @param {Object} data - The dispatch data
 * @param {string} data.driver_name - Name of the driver
 * @param {string} data.driver_contact - Contact number of the driver
 * @param {string} data.vehicle_number - Vehicle number for transportation
 * @param {string} data.transaction_date - ISO format date-time of the dispatch
 * @param {number} data.mandi_number_id - Mandi number ID
 * @param {Array} data.lots - Array of lot objects to dispatch
 * @param {number} data.lots[].lot_id - Lot ID
 * @param {number} data.lots[].dispatched_units - Number of units to dispatch
 * @returns {Promise} - API response
 */
export const dispatchInventory = async (data) => {
  try {
    const response = await mandiService.post('/inventory/transactions', data);
    console.log('Dispatch response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error dispatching inventory:', error);
    throw error;
  }
};

/**
 * Fetch inventory transactions for a specific mandi number ID
 * @param {number} mandiNumberId - The mandi number ID to fetch transactions for
 * @returns {Promise} - API response with transaction data
 */
export const fetchInventoryTransactions = async (mandiNumberId) => {
  try {
    if (!mandiNumberId) {
      throw new Error('Mandi number ID is required');
    }
    
    const response = await mandiService.get(`/inventory/transactions`, {
      params: { mandi_number_id: mandiNumberId }
    });
    
    if (response.responseData) {
      return response.responseData;
    }
    return [];
  } catch (error) {
    console.error(`Error fetching transactions for mandi number ID ${mandiNumberId}:`, error);
    throw error;
  }
};

import { PACKAGING_ITEM } from 'Utilities/constants';

import { mandiService, supplyChainService } from './base';

export const getFarmers = params => {
  return supplyChainService.get('farmers.json/', { params });
};

export const getFarmersById = params => {
  return supplyChainService.get('partners/list.json?Role=Farmer', { params });
};

export const getTransportersById = params => {
  return supplyChainService.get('partners/list.json', {
    params: { transporters: true, ...params },
  });
};

export const getProducts = () => {
  return supplyChainService.get('products.json/');
};

export const getSkus = params => {
  return supplyChainService.get('skus.json', { params });
};

export const getPackagingType = () => {
  return supplyChainService.get('products/packaging_types.json');
};

export const getMandis = params => {
  return supplyChainService.get('mandis.json', { params });
};

export const getMandiConfig = params => {
  return mandiService.get('auctionsettings', { params });
};

export const getSatelliteCCs = params => {
  return mandiService.get('/getsatelliteccs', { params });
};

export const getPackagingBoms = () => {
  const params = Object.values(PACKAGING_ITEM)
    .map(value => {
      return 'mandi_identifier[]=' + value;
    })
    .join('&');

  return mandiService.get('/packaging-boms', { params });
};

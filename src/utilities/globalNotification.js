import { enqueueSnackbar } from 'notistack';

/**
 * Global notification utility that can be used from anywhere in the app
 * including service files and non-React contexts
 */
class GlobalNotification {
  /**
   * Show a notification message
   * @param {string} message - The message to display
   * @param {string} variant - The type of notification ('success', 'error', 'warning', 'info')
   */
  static notify(message, variant = 'error') {
    try {
      enqueueSnackbar(message, {
        variant,
        anchorOrigin: {
          vertical: 'top',
          horizontal: 'right',
        },
        autoHideDuration: variant === 'error' ? 5000 : 3000, // Show errors longer
      });
    } catch (error) {
      // Fallback to console if snackbar is not available
      console.error('Notification error:', error);
      console.log(`${variant.toUpperCase()}: ${message}`);
    }
  }

  /**
   * Show success notification
   * @param {string} message
   */
  static success(message) {
    this.notify(message, 'success');
  }

  /**
   * Show error notification
   * @param {string} message
   */
  static error(message) {
    this.notify(message, 'error');
  }

  /**
   * Show warning notification
   * @param {string} message
   */
  static warning(message) {
    this.notify(message, 'warning');
  }

  /**
   * Show info notification
   * @param {string} message
   */
  static info(message) {
    this.notify(message, 'info');
  }
}

export default GlobalNotification;

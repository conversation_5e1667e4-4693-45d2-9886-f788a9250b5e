import { getCustomersName } from './constants';
import { extractGradeType } from './constants/lots';

// Create dynamic grade mapping from SKU sizes API response
export const createGradeMappingFromSkuSizes = (skuSizesData) => {
  if (!skuSizesData || !skuSizesData.items || !Array.isArray(skuSizesData.items)) return {};
  
  const mapping = {};
  skuSizesData.items.forEach(({ size }) => {
    if (size) {
      let key;
      
      // Handle specific patterns first (before general patterns)
      if (size === 'Pitoo (310)') {
        key = 'Pitto_310'; // Match the existing hardcoded format (note: Pitto not Pitoo)
      } else if (size === 'EES (210/240)') {
        key = 'EES_200'; // Map to the expected format
      } else if (size === 'Open Septt. -6L') {
        key = 'Mix_6L'; // Map to the expected format
      } else if (size === 'Mix Garde') {
        key = 'Mix';
      } else if (size === 'Other') {
        key = 'Other';
      } else if (size.match(/\(\d+\)/)) {
        // Standard format like "EL (80)", "L (100)", etc.
        key = size.replace(/\s*\((\d+)\)/, '_$1').replace(/\s+/g, '_');
      } else {
        // Fallback: replace spaces with underscores and remove special chars
        key = size.replace(/[^\w\s]/g, '').replace(/\s+/g, '_');
      }
      
      mapping[size] = key;
    }
  });
  
  return mapping;
};

// Enhanced function to get SKU size ID from grade string
export const getSkuSizeIdFromGrade = (gradeString, skuSizes) => {
  if (!gradeString || !skuSizes || !Array.isArray(skuSizes.items)) return null;
  
  // Remove the prefix like "A-" from the grade string to get just the size part
  const gradePart = gradeString.replace(/^[A-Z]-/, '');
  
  // Find the matching SKU size by comparing the grade part with the size
  const matchingSku = skuSizes.items.find(sku => sku.size === gradePart);
  
  return matchingSku ? matchingSku.id : null;
};

export const transformApiDataToPriceSlipFormat = (apiData, farmerTokenId, skuSizes = null) => {
  if (!apiData?.responseData || !farmerTokenId) {
    return null;
  }

  const tokenData = apiData.responseData[farmerTokenId];
  if (!tokenData) {
    return null;
  }

  const {
    lots_data = [],
    farmer_name = '',
    farmer_address = '',
    gatein_identifier = '',
    auction_date,
    gatein_created_at,
    total_expenses = 0,
    net_amount = 0,
    farmer_sub_total = 0,
  } = tokenData;

  // Group lots by mandi number and lot identifier
  const groupedData = {};

  lots_data.forEach((lot) => {
    const {
      mandi_number,
      gatein_product_name,
      grade,
      selling_price_per_unit,
      units,
      customer_id,
      customer_name,
      customer_short_code,
      pack_type,
    } = lot;

    const mandiNo = mandi_number?.mandi_number || '';
    const lotIdentifier = mandi_number?.grade || '';
    
    // Get SKU size ID from grade string using the new function
    const skuSizeId = skuSizes 
      ? getSkuSizeIdFromGrade(grade, skuSizes)
      : extractGradeType(grade); // fallback to old method

    if (!skuSizeId) return;

    // Use lot identifier (like "A", "B", "C") as the key to group all grades of the same lot
    const key = `${mandiNo}-${lotIdentifier}`;

    // Get customer name using the utility function
    const customerInfo = getCustomersName({
      customer_id,
      customer_name,
      customer_short_code,
    });

    if (!groupedData[key]) {
      groupedData[key] = {
        mandiNo,
        variety: gatein_product_name || '',
        lot: lotIdentifier,
        packType: pack_type || '',
        grades: {},
        amount: 0,
        // Store customer info for loader display
        customerName: customerInfo.name,
        customerShortCode: customerInfo.customer_short_code,
      };
    } else {
      // If the lot already exists, update pack_type if it's not set or if current one has a value
      if (!groupedData[key].packType && pack_type) {
        groupedData[key].packType = pack_type;
      }
    }

    // Add grade data using SKU size ID as the key
    if (!groupedData[key].grades[skuSizeId]) {
      groupedData[key].grades[skuSizeId] = {
        price: selling_price_per_unit || 0,
        quantity: units || 0,
        loader: customerInfo.customer_short_code || customerInfo.name || '',
      };
    } else {
      // If grade already exists for this lot, add quantities
      groupedData[key].grades[skuSizeId].quantity += units || 0;
      // Use the higher price if there are multiple entries for the same grade
      groupedData[key].grades[skuSizeId].price = Math.max(
        groupedData[key].grades[skuSizeId].price,
        selling_price_per_unit || 0
      );
    }

    // Calculate amount for this lot
    groupedData[key].amount += (selling_price_per_unit || 0) * (units || 0);
  });

  const transformedLotsData = Object.values(groupedData).map(lot => ({
    ...lot,
    // Use the customer info for loader if not already set
    loader: lot.customerShortCode || lot.customerName || '',
  }));

  // Calculate total amounts
  const totalAmountCalculated = transformedLotsData.reduce((sum, lot) => sum + lot.amount, 0);

  const transformedData = {
    data: transformedLotsData,
    auctionDate: new Date(auction_date).toISOString(),
    createdAt: new Date(gatein_created_at || Date.now()).toISOString(),
    farmerName: farmer_name,
    address: farmer_address,
    tokenNumber: gatein_identifier,
    mobileNumber: tokenData.phone_number || '',
    vehicleNumber: tokenData.vehicle_number || lots_data[0]?.vehicle_number || '',
    transporterName: tokenData.transporter_name || '', 
    boxesBrought: lots_data.reduce((sum, lot) => sum + (lot.units || 0), 0).toString(),
    amountData: {
      totalAmount: farmer_sub_total || totalAmountCalculated,
      lessExpenses: total_expenses || 0,
      finalAmount: net_amount || totalAmountCalculated,
    },
  };

  return transformedData;
};


export const getBrandLogo = () => {
  return '/logo.png'; 
};


export const getMandiName = (siteContext) => {
  return siteContext?.mandi_name || 'Chifu Agritech Pvt. Ltd.';
};

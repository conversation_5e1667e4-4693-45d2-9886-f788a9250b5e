import { useEffect, useRef, useState } from 'react';

import { Tab, Tabs, Typo<PERSON>, Button } from '@mui/material';
import { isEmpty } from 'lodash';
import queryString from 'query-string';
import { useNavigate } from 'react-router-dom';
import { useReactToPrint } from 'react-to-print';

import { useSiteValue } from 'App/SiteContext';
import {
  AppLoader,
  NoDataAvailable,
  PageFilter,
  Pagination as CustomPagination,
  Table,
  Modal as CustomModal,
} from 'Components';
import FarmerBill from 'Components/Bills/FarmerBill';
import MandiSelectionModal from 'Components/MandiSelectionModal';
import PageLayout from 'Components/PageLayout/index.jsx';
import PrintCustomerSlip from 'Components/PrintSlips/PrintCustomerSlip/PrintCustomerSlip';
import PrintTokenSlipPriceCard from 'Components/PrintSlips/PrintTokenSlipPriceCard';
import PrintTokenSlipWrapper from 'Components/PrintSlips/PrintTokenSlipWrapper';
import API from 'Config/envConfig';
import useMandiConfig from 'Hooks/useMandiConfig';
import useNotify from 'Hooks/useNotify';
import { getFarmerBill } from 'Services/bills';
import { getFarmers } from 'Services/common';
import {
  createPR,
  getAdvancePayment,
  getPaymentRequestById,
  updatePR,
} from 'Services/payments';
import { getSkuSizes } from 'Services/regrade';
import {
  getTabsCount,
  getToBeGraded,
  getTokenListing,
  updateTokenAttachments,
} from 'Services/token';
import {
  INWARD_TYPE,
  INWARD_TYPE_OPTIONS,
  PAGE_SIZE,
} from 'Utilities/constants';
import {
  STATUS,
  TAB_VALUES,
  TOKEN_STATUS_LIST,
  TOKEN_TABS,
} from 'Utilities/constants/lots';
import { getFormattedDate, getStartOfToday } from 'Utilities/dateUtils';
import fileUpload from 'Utilities/fileUpload';
import {
  transformApiDataToPriceSlipFormat,
  getBrandLogo,
} from 'Utilities/priceSlipUtils';

import ValueBreakDownModal from '../components/ValueBreakDownModal';

import PaymentAction from './paymentAction';
import PaymentRequestModal from './PaymentRequestModal';
import { TabWrapper } from './Styled';

const initialValues = {
  auction_date: getStartOfToday(),
  type: null,
  token_id: null,
  farmer: null,
};

const TokenPage = () => {
  const ref = useRef(false);
  const apiCallRef = useRef(false);
  const supplyBillRef = useRef();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);
  const [tab, setTab] = useState(STATUS.AUCTION_READY);
  const [open, setOpen] = useState(false);
  const [tokenList, setTokenListing] = useState([]);
  const [filters, setFilters] = useState({});
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(1);
  const [counts, setCounts] = useState({});
  const [data, setData] = useState({});
  const [farmers, setFarmers] = useState([]);
  const { userInfo, mandiId, mandiList, dcId, mandiConfig } = useSiteValue();
  const [mandi, setMandi] = useState({});
  const [prData, setPRData] = useState({});
  const [openBreakDownModal, setOpenBreakDownModal] = useState(false);
  const [lotsData, setLotsData] = useState([]);
  const [showBill, shouldShowBill] = useState(false);
  const [rowData, setRowData] = useState({});
  const [type, setType] = useState('');
  const [modalOpen, setModalOpen] = useState(false);
  const [undoData, setUndoData] = useState({});
  const [footerValue, setFooterValue] = useState({});
  const [advancePayment, setAdvancePayment] = useState([]);
  const NotificationBar = useNotify();
  const [advancePrData, setAdvancePrData] = useState([]);
  const [recordArrival, setRecordArrival] = useState(false);
  const setMandiData = useMandiConfig();
  const {
    PR_RAISED,
    PR_APPROVED,
    FINANCE_APPROVED,
    PAID,
    CANCELLED,
    SCHEDULED_TO_PAY,
  } = TOKEN_TABS;
  const [breakDownModalLoading, setBreakDownModalLoading] = useState(false);
  const [priceSlipData, setPriceSlipData] = useState(null);
  const [showPriceSlipPreview, setShowPriceSlipPreview] = useState(false);
  // const priceSlipRef = useRef(); // No longer needed - handled internally in wrapper
  const [customerSlipData, setCustomerSlipData] = useState(null);
  const [showCustomerSlip, setShowCustomerSlip] = useState(false);
  const customerSlipRef = useRef();
  const [showMandiSelectionModal, setShowMandiSelectionModal] = useState(false);
  const [pendingCustomerSlipData, setPendingCustomerSlipData] = useState(null);
  const [skuSizesData, setSkuSizesData] = useState([]);

  const handleModalToggle = data => {
    setUndoData(data);
    setModalOpen(!modalOpen);
  };

  useEffect(() => {
    const mandi = mandiList?.find(item => item?.id === mandiId) || {};
    setMandi(mandi);
  }, [mandiList, mandiId]);

  const loadTokenListing = ({
    newPage = page,
    currentFilter = filters,
    currentTab = tab,
  }) => {
    // Prevent duplicate calls using ref
    if (apiCallRef.current) {
      return;
    }

    apiCallRef.current = true;
    setLoading(true);
    setTokenListing([]);
    const statuses = [STATUS.TO_BE_GRADED, STATUS.IN_SATELLITE_MANDI];
    const getData = statuses.includes(currentTab)
      ? getToBeGraded
      : getTokenListing;

    const requestParams = () => {
      const foundStatus = [
        PR_RAISED,
        PR_APPROVED,
        FINANCE_APPROVED,
        PAID,
        CANCELLED,
        SCHEDULED_TO_PAY,
      ].find(t => t === currentTab);

      if (foundStatus) {
        return { [foundStatus.toLowerCase()]: true };
      }

      return { status: currentTab };
    };

    const apiParams = {
      ...currentFilter,
      mandi_id: mandiId,
      ...requestParams(),
      offset: (newPage - 1) * PAGE_SIZE,
      limit: PAGE_SIZE,
      // Add include_discount_view for Auction Ready and Sold tabs
      ...([STATUS.AUCTION_READY, STATUS.SOLD].includes(currentTab) && {
        include_discount_view: true,
      }),
    };

    // Create promises array - always include the main API calls
    const promises = [
      getData(apiParams),
      getTabsCount({
        mandi_id: mandiId || '',
        ...currentFilter,
      }),
    ];

    // Add SKU sizes API call when Sold tab is selected
    if (currentTab === STATUS.SOLD && mandiId) {
      promises.push(getSkuSizes(mandiId));
    }

    Promise.all(promises)
      .then(res => {
        if (res) {
          const contData = res[1].responseData.total_counts;
          const tokenData = res[0].responseData || [];

          setTokenListing(tokenData);
          setCounts(contData || {});
          setTotalCount(
            Math.ceil(contData[currentTab.toLowerCase()] / PAGE_SIZE) || 0
          );

          // Handle SKU sizes data if Sold tab is selected
          if (currentTab === STATUS.SOLD && res[2]) {
            const skuSizesResponse = res[2];
            const skuData = skuSizesResponse?.data || skuSizesResponse;

            // Store SKU sizes data in state for dynamic grade mapping
            setSkuSizesData(skuData?.items || []);
          }
        }
      })
      .catch(() => {
        NotificationBar('Unable to load token listing data', 'error');
      })
      .finally(() => {
        apiCallRef.current = false;
        setLoading(false);
      });
  };

  useEffect(() => {
    //! ref is used as PageFilter is making initial request
    if (!ref.current) {
      ref.current = true;
    } else {
      loadTokenListing({ newPage: page });
    }
  }, [tab, mandiId, recordArrival, page, filters]);

  useEffect(() => {
    !isEmpty(rowData) && toggleShowBill();
  }, [rowData]);

  const submitFilters = ({
    token_id,
    auction_date = getStartOfToday(),
    farmer,
    type,
  }) => {
    setTokenListing([]);
    const currentFilter = {
      ...(token_id ? { token: `${type?.value}-${token_id}` } : {}),
      ...(auction_date ? { auction_date } : {}),
      ...(farmer?.id ? { farmer_id: farmer.id } : {}),
      ...(type ? { inward_type: type?.text } : {}),
    };

    setMandiData(auction_date);
    setFilters(currentFilter);
    loadTokenListing({ currentFilter });
  };

  useEffect(() => {
    if (data?.farmer_id) {
      getAdvancePayment({ vendor_id: data?.farmer_id }).then(
        ({ responseData }) => setAdvancePayment(responseData?.items)
      );
    }
  }, [data]);

  useEffect(() => {
    if (data?.farmer_id) {
      getPaymentRequest(data);
    }
  }, [advancePayment, data]);

  const handleChangeTab = (_, newValue) => {
    setTab(newValue);
    setPage(1);
  };

  const handleChangePage = (_, newPage) => {
    setPage(newPage);
  };

  const openModal = data => {
    setData(data);
    setOpen(true);
    const params = {
      token: data?.token,
      auctionDate: data?.auction_date,
      isFarmerBill: true,
      mandiId,
    };
    getFarmerBill(params).then(({ responseData = {} }) =>
      setFooterValue({ ...responseData })
    );
  };

  const openBreakDownModalFnc = data => {
    setOpenBreakDownModal(true);
    setLotsData(data);
    setBreakDownModalLoading(true);
    const params = {
      token: data?.token,
      auctionDate: data?.auction_date,
      isFarmerBill: true,
      mandiId,
    };
    getFarmerBill(params)
      .then(({ responseData = {} }) => {
        setFooterValue({ ...responseData });
      })
      .finally(() => {
        setBreakDownModalLoading(false);
      });
  };

  const handlePrint = useReactToPrint({
    content: () => supplyBillRef.current,
  });

  const toggleShowBill = () => {
    !!showBill && setRowData({});
    shouldShowBill(!showBill);
  };

  //! change for farmerBill, open the farmer bill in a new window
  const handleFarmerBill = ({ lots_data = [] }) => {
    const URL = {
      pathname: `${API.mandiService}/farmerbill.pdf?`,
      search: queryString.stringify({
        tracking_id: lots_data?.[0]?.tracking_id,
      }),
    };
    window.open(URL.pathname + URL.search);
  };

  const handlePreview = data => {
    setRowData(data);
  };

  // Handle price slip download
  const handleDownloadPriceSlip = async rowData => {
    try {
      const farmerTokenId = rowData?.lots_data?.[0]?.farmer_token_id;
      if (!farmerTokenId) {
        NotificationBar('Unable to find token information', 'error');
        return;
      }

      // Fetch SKU sizes to create dynamic grade mapping
      let skuSizesData = null;
      try {
        const skuSizesResponse = await getSkuSizes(mandiId);
        skuSizesData = skuSizesResponse?.data || skuSizesResponse; // Try both .data and direct response

        // Check if the API response structure is correct
        if (!skuSizesData?.items || skuSizesData.items.length === 0) {
          // Try direct access if .data didn't work
          if (skuSizesResponse?.items && skuSizesResponse.items.length > 0) {
            skuSizesData = skuSizesResponse;
          }
        }
      } catch (error) {
        // Will fall back to the old extractGradeType method in transformApiDataToPriceSlipFormat
        NotificationBar(
          'Warning: Could not fetch grade mappings, using defaults',
          'warning'
        );
      }

      // Create API data structure similar to what the API returns
      const apiData = {
        responseData: {
          [farmerTokenId]: rowData,
        },
      };

      // Pass the SKU sizes data to the transform function
      const transformedData = transformApiDataToPriceSlipFormat(
        apiData,
        farmerTokenId,
        skuSizesData
      );

      if (!transformedData) {
        NotificationBar('Unable to generate price slip data', 'error');
        return;
      }

      // Get brand logo from mandi list
      const currentMandi = mandiList?.find(m => m.id === mandiId);

      const brandLogo =
        currentMandi?.brand_url ||
        currentMandi?.logo_url ||
        currentMandi?.mandi_logo ||
        currentMandi?.brand_logo ||
        getBrandLogo();

      // Add additional required props including skuSizes
      const priceSlipProps = {
        ...transformedData,
        tokenNumber:
          rowData?.lots_data?.[0]?.token || transformedData.tokenNumber || '',
        brandLogo: brandLogo,
        mandiName: currentMandi?.name || '',
        mobileNumber:
          rowData?.lots_data?.[0]?.phone_number ||
          transformedData.mobileNumber ||
          '',
        auctionDate: rowData?.auction_date || new Date().toISOString(),
        createdAt: new Date().toISOString(),
        transporterName:
          rowData?.transporter_name || transformedData.transporterName || '',
        vehicleNumber:
          rowData?.vehicle_number || transformedData.vehicleNumber || '',
        skuSizes: skuSizesData?.items || [],
        hasTransporterPayment: (rowData?.transportation_cost || 0) > 0,
      };

      setPriceSlipData(priceSlipProps);

      setShowPriceSlipPreview(true);
    } catch (error) {
      NotificationBar('Error generating price slip', 'error');
    }
  };

  const togglePriceSlipPreview = () => {
    setShowPriceSlipPreview(!showPriceSlipPreview);
    if (!showPriceSlipPreview) {
      setPriceSlipData(null);
    }
  };

  // Print is now handled internally in PrintTokenSlipWrapper

  // Handle customer slip preview - Group by customer code and show consolidated slip
  const handleDownloadCustomerSlip = rowData => {
    try {
      const farmerTokenId = rowData?.lots_data?.[0]?.farmer_token_id;
      if (!farmerTokenId) {
        NotificationBar('Unable to find token information', 'error');
        return;
      }

      // Group lots by customer code
      const lotsByCustomer = {};
      rowData?.lots_data?.forEach((lot, index) => {
        const customerCode = lot?.customer_short_code || 'Unknown';
        
        if (!lotsByCustomer[customerCode]) {
          lotsByCustomer[customerCode] = [];
        }
        lotsByCustomer[customerCode].push(lot);
      });

      const customerCodes = Object.keys(lotsByCustomer);

      if (customerCodes.length === 1) {
        // If only one customer, show consolidated slip directly
        generateConsolidatedCustomerSlip(rowData, customerCodes[0]);
      } else {
        // Multiple customers - generate all consolidated slips in one PDF
        generateAllCustomerSlipsInOnePDF(rowData, customerCodes);
      }
    } catch (error) {
      console.error('Error generating customer slip:', error);
      NotificationBar('Error generating customer slip', 'error');
    }
  };

  // Handle direct customer slip download - Direct print without preview (for download icon)
  const handleDirectCustomerSlipDownload = rowData => {
    try {
      const farmerTokenId = rowData?.lots_data?.[0]?.farmer_token_id;
      if (!farmerTokenId) {
        NotificationBar('Unable to find token information', 'error');
        return;
      }

      // Group lots by mandi number to get available mandi numbers
      const lotsByMandiNumber = {};
      rowData?.lots_data?.forEach((lot, index) => {
        const mandiNumber = lot?.mandi_number?.mandi_number || `A${index + 1}`;
        if (!lotsByMandiNumber[mandiNumber]) {
          lotsByMandiNumber[mandiNumber] = [];
        }
        lotsByMandiNumber[mandiNumber].push(lot);
      });

      const mandiNumbers = Object.keys(lotsByMandiNumber);

      if (mandiNumbers.length === 1) {
        // If only one mandi number, download directly
        generateCustomerSlipForMandi(rowData, mandiNumbers[0]);
      } else {
        // Show popup for mandi selection for direct download
        setPendingCustomerSlipData(rowData);
        setShowMandiSelectionModal(true);
      }
    } catch (error) {
      NotificationBar('Error generating customer slip', 'error');
    }
  };

  // Generate consolidated customer slip for a specific customer code
  const generateConsolidatedCustomerSlip = (rowData, selectedCustomerCode) => {
    try {
      // Group lots by customer code first
      const lotsByCustomer = {};
      rowData?.lots_data?.forEach((lot, index) => {
        const customerCode = lot?.customer_short_code || 'Unknown';
        if (!lotsByCustomer[customerCode]) {
          lotsByCustomer[customerCode] = [];
        }
        lotsByCustomer[customerCode].push(lot);
      });

      // Get only the lots for the selected customer
      const lotsForSelectedCustomer = lotsByCustomer[selectedCustomerCode] || [];

      const groupedData = {};
      
      lotsForSelectedCustomer.forEach((lot, lotIndex) => {
        const variety = lot?.product_name || '';
        const lotGrade = lot?.mandi_number?.grade || '';
        
        // Extract mandi number from different possible locations
        let mandiNumber = '';
        if (lot?.mandi_number?.mandi_number) {
          mandiNumber = lot.mandi_number.mandi_number;
        } else if (lot?.mandi_number && typeof lot.mandi_number === 'string') {
          mandiNumber = lot.mandi_number;
        } else {
          mandiNumber = `A${lotIndex + 1}`;
        }

        const key = `${variety}-${lotGrade}-${mandiNumber}`;

        if (!groupedData[key]) {
          groupedData[key] = {
            mandiNo: mandiNumber, // Add mandi number to each row
            variety,
            lot: lotGrade,
            packType: lot?.pack_type || '',
            grades: {}, // Dynamic grades will be added as needed
          };
        }

        // Get the grade name dynamically using mandi_sku_size_id
        let gradeKey = 'Unknown';
        
        if (lot?.mandi_sku_size_id && skuSizesData?.length > 0) {
          // Find the SKU size by ID from the API data
          const skuSize = skuSizesData.find(sku => sku.id === lot.mandi_sku_size_id);
          
          if (skuSize) {
            gradeKey = skuSize.size || 'Unknown';
          }
        } else if (lot?.grade) {
          // Fallback to lot.grade if mandi_sku_size_id is not available
          gradeKey = lot.grade;
        }

        // Initialize the grade in our structure if it doesn't exist
        if (!groupedData[key].grades[gradeKey]) {
          groupedData[key].grades[gradeKey] = { quantity: 0, price: 0 };
        }

        // Add quantities and use higher price if multiple entries for same grade
        groupedData[key].grades[gradeKey].quantity += lot?.units || 0;
        groupedData[key].grades[gradeKey].price = Math.max(
          groupedData[key].grades[gradeKey].price,
          lot?.selling_price_per_unit || 0
        );
      });

      const transformedLotData = Object.values(groupedData);

      // Calculate proportional amounts for this customer
      const totalLots = rowData?.lots_data?.length || 1;
      const selectedCustomerLots = lotsForSelectedCustomer.length;
      const proportionalSubTotal = Math.round(
        (rowData?.sub_total || 0) * (selectedCustomerLots / totalLots)
      );
      const proportionalNetAmount = Math.round(
        (rowData?.net_amount || 0) * (selectedCustomerLots / totalLots)
      );

      // Use proportional amounts if multiple customers, otherwise use full amounts
      const displaySubTotal =
        totalLots === selectedCustomerLots
          ? rowData?.sub_total || 0
          : proportionalSubTotal;
      const displayNetAmount =
        totalLots === selectedCustomerLots
          ? rowData?.net_amount || 0
          : proportionalNetAmount;

      const customerName = lotsForSelectedCustomer?.[0]?.customer_name || '';
      const currentMandi = mandiList?.find(m => m.id === mandiId);
      const brandLogo = currentMandi?.brand_url || currentMandi?.logo_url || currentMandi?.mandi_logo || currentMandi?.brand_logo || getBrandLogo();

      const customerSlipProps = {
        tokenNumber: rowData?.token || '',
        farmerName: rowData?.farmer_name || '',
        date: rowData?.auction_date || new Date().toISOString(),
        brandLogo: brandLogo,
        lotData: transformedLotData,
        totalAmount: displaySubTotal,
        netAmount: displayNetAmount,
        totalExpenses: rowData?.total_expenses || 0,
        skuSizes: skuSizesData || [], // Pass the SKU sizes data from API
        customerShortCode: selectedCustomerCode,
        customerName: customerName, 
      };

      setCustomerSlipData(customerSlipProps);
      setShowCustomerSlip(true);
    } catch (error) {
      console.error('Error in generateConsolidatedCustomerSlip:', error);
      NotificationBar('Error generating consolidated customer slip', 'error');
    }
  };

  // Generate customer slip preview for specific mandi number
  const generateCustomerSlipPreview = (rowData, selectedMandiNumber) => {
    try {
      // Group lots by mandi number
      const lotsByMandiNumber = {};
      rowData?.lots_data?.forEach((lot, index) => {
        const mandiNumber = lot?.mandi_number?.mandi_number || `A${index + 1}`;
        if (!lotsByMandiNumber[mandiNumber]) {
          lotsByMandiNumber[mandiNumber] = [];
        }
        lotsByMandiNumber[mandiNumber].push(lot);
      });

      const lotsForSelectedMandi = lotsByMandiNumber[selectedMandiNumber] || [];

      const groupedData = {};

      lotsForSelectedMandi.forEach((lot, lotIndex) => {
        const variety = lot?.product_name || '';
        const lotGrade = lot?.mandi_number?.grade || '';
        const key = `${variety}-${lotGrade}`;

        if (!groupedData[key]) {
          // Find pack type information from the first lot of this group
          let packType = '';
          if (lot?.mandi_sku_size_id && skuSizesData?.length > 0) {
            const skuSize = skuSizesData.find(
              sku => sku.id === lot.mandi_sku_size_id
            );
            if (skuSize) {
              packType = skuSize.pack_type || skuSize.packType || '';
            }
          }

          groupedData[key] = {
            variety,
            lot: lotGrade,
            packType: lot?.pack_type || '', // Use pack_type directly from lot data
            grades: {}, // Dynamic grades will be added as needed
          };
        }

        // Get the grade name dynamically using mandi_sku_size_id
        let gradeKey = 'Unknown';

        if (lot?.mandi_sku_size_id && skuSizesData?.length > 0) {
          // Find the SKU size by ID from the API data
          const skuSize = skuSizesData.find(
            sku => sku.id === lot.mandi_sku_size_id
          );

          if (skuSize) {
            gradeKey = skuSize.size || 'Unknown';
          }
        } else if (lot?.grade) {
          // Fallback to lot.grade if mandi_sku_size_id is not available
          gradeKey = lot.grade;
        }

        // Initialize the grade in our structure if it doesn't exist
        if (!groupedData[key].grades[gradeKey]) {
          groupedData[key].grades[gradeKey] = { quantity: 0, price: 0 };
        }

        // Add quantities and use higher price if multiple entries for same grade
        groupedData[key].grades[gradeKey].quantity += lot?.units || 0;
        groupedData[key].grades[gradeKey].price = Math.max(
          groupedData[key].grades[gradeKey].price,
          lot?.selling_price_per_unit || 0
        );
      });

      const transformedLotData = Object.values(groupedData);

      // Calculate proportional amounts for this mandi if multiple mandis exist
      const totalLots = rowData?.lots_data?.length || 1;
      const selectedMandiLots = lotsForSelectedMandi.length;
      const proportionalSubTotal = Math.round(
        (rowData?.sub_total || 0) * (selectedMandiLots / totalLots)
      );
      const proportionalNetAmount = Math.round(
        (rowData?.net_amount || 0) * (selectedMandiLots / totalLots)
      );

      // Use proportional amounts if multiple mandis, otherwise use full amounts
      const displaySubTotal =
        totalLots === selectedMandiLots
          ? rowData?.sub_total || 0
          : proportionalSubTotal;
      const displayNetAmount =
        totalLots === selectedMandiLots
          ? rowData?.net_amount || 0
          : proportionalNetAmount;

      const currentMandi = mandiList?.find(m => m.id === mandiId);
      const brandLogo =
        currentMandi?.brand_url ||
        currentMandi?.logo_url ||
        currentMandi?.mandi_logo ||
        currentMandi?.brand_logo ||
        getBrandLogo();

      const customerSlipProps = {
        tokenNumber: rowData?.token || '',
        farmerName: rowData?.farmer_name || '',
        date: rowData?.auction_date || new Date().toISOString(),
        brandLogo: brandLogo,
        mandiNo: selectedMandiNumber,
        lotData: transformedLotData,
        totalAmount: displaySubTotal,
        netAmount: displayNetAmount,
        totalExpenses: rowData?.total_expenses || 0,
        skuSizes: skuSizesData || [], // Pass the SKU sizes data from API
        customerShortCode: lotsForSelectedMandi?.[0]?.customer_short_code || '', // Add customer_short_code
      };

      setCustomerSlipData(customerSlipProps);
      setShowCustomerSlip(true);
    } catch (error) {
      console.error('Error in generateCustomerSlipPreview:', error);
      NotificationBar('Error generating customer slip preview', 'error');
    }
  };

  // Generate customer slip for specific mandi number (for direct printing)
  const generateCustomerSlipForMandi = (rowData, selectedMandiNumber) => {
    try {
      // Group lots by mandi number
      const lotsByMandiNumber = {};
      rowData?.lots_data?.forEach((lot, index) => {
        const mandiNumber = lot?.mandi_number?.mandi_number || `A${index + 1}`;
        if (!lotsByMandiNumber[mandiNumber]) {
          lotsByMandiNumber[mandiNumber] = [];
        }
        lotsByMandiNumber[mandiNumber].push(lot);
      });

      const lotsForSelectedMandi = lotsByMandiNumber[selectedMandiNumber] || [];

      const groupedData = {};

      lotsForSelectedMandi.forEach((lot, lotIndex) => {
        const variety = lot?.product_name || '';
        const lotGrade = lot?.mandi_number?.grade || '';
        const key = `${variety}-${lotGrade}`;

        if (!groupedData[key]) {
          // Find pack type information from the first lot of this group
          let packType = '';
          if (lot?.mandi_sku_size_id && skuSizesData?.length > 0) {
            const skuSize = skuSizesData.find(
              sku => sku.id === lot.mandi_sku_size_id
            );
            if (skuSize) {
              packType = skuSize.pack_type || skuSize.packType || '';
            }
          }

          groupedData[key] = {
            variety,
            lot: lotGrade,
            packType: lot?.pack_type || lot?.mandi_pack_name || '',
            grades: {},
          };
        }

        // Get the grade name dynamically using mandi_sku_size_id
        let gradeKey = 'Unknown';

        if (lot?.mandi_sku_size_id && skuSizesData?.length > 0) {
          // Find the SKU size by ID from the API data
          const skuSize = skuSizesData.find(
            sku => sku.id === lot.mandi_sku_size_id
          );

          if (skuSize) {
            gradeKey = skuSize.size || 'Unknown';
          }
        } else if (lot?.grade) {
          gradeKey = lot.grade;
        }

        if (!groupedData[key].grades[gradeKey]) {
          groupedData[key].grades[gradeKey] = { quantity: 0, price: 0 };
        }

        groupedData[key].grades[gradeKey].quantity += lot?.units || 0;
        groupedData[key].grades[gradeKey].price = Math.max(
          groupedData[key].grades[gradeKey].price,
          lot?.selling_price_per_unit || 0
        );
      });

      const transformedLotData = Object.values(groupedData);

      // Calculate proportional amounts for this mandi if multiple mandis exist
      const totalLots = rowData?.lots_data?.length || 1;
      const selectedMandiLots = lotsForSelectedMandi.length;
      const proportionalSubTotal = Math.round(
        (rowData?.sub_total || 0) * (selectedMandiLots / totalLots)
      );
      const proportionalNetAmount = Math.round(
        (rowData?.net_amount || 0) * (selectedMandiLots / totalLots)
      );

      const displaySubTotal =
        totalLots === selectedMandiLots
          ? rowData?.sub_total || 0
          : proportionalSubTotal;
      const displayNetAmount =
        totalLots === selectedMandiLots
          ? rowData?.net_amount || 0
          : proportionalNetAmount;

      const currentMandi = mandiList?.find(m => m.id === mandiId);
      const brandLogo =
        currentMandi?.brand_url ||
        currentMandi?.logo_url ||
        currentMandi?.mandi_logo ||
        currentMandi?.brand_logo ||
        getBrandLogo();

      const customerSlipProps = {
        tokenNumber: rowData?.token || '',
        farmerName: rowData?.farmer_name || '',
        date: rowData?.auction_date || new Date().toISOString(),
        brandLogo: brandLogo,
        mandiNo: selectedMandiNumber,
        lotData: transformedLotData,
        totalAmount: displaySubTotal,
        netAmount: displayNetAmount,
        totalExpenses: rowData?.total_expenses || 0,
        skuSizes: skuSizesData || [],
      };

      setCustomerSlipData(customerSlipProps);
      setShowCustomerSlip(true);

      // Trigger print after component is rendered
      setTimeout(() => {
        if (customerSlipRef.current) {
          handlePrintCustomerSlip();
        } else {
          NotificationBar('Customer slip component not ready', 'error');
        }
      }, 1000);
    } catch (error) {
      console.error('Error in generateCustomerSlipForMandi:', error);
      NotificationBar('Error generating customer slip', 'error');
    }
  };

  // Generate all customer slips in one PDF - one page per customer
  const generateAllCustomerSlipsInOnePDF = (rowData, customerCodes) => {
    try {
      // Group lots by customer code first
      const lotsByCustomer = {};
      rowData?.lots_data?.forEach((lot, index) => {
        const customerCode = lot?.customer_short_code || 'Unknown';
        if (!lotsByCustomer[customerCode]) {
          lotsByCustomer[customerCode] = [];
        }
        lotsByCustomer[customerCode].push(lot);
      });

      // Generate page data for each customer
      const pageData = customerCodes.map((customerCode) => {
        const lotsForCustomer = lotsByCustomer[customerCode] || [];

        // Extract customer name from the first lot of this customer
        const customerName = lotsForCustomer?.[0]?.customer_name || '';

        const groupedData = {};
        
        lotsForCustomer.forEach((lot, lotIndex) => {
          const variety = lot?.product_name || '';
          const lotGrade = lot?.mandi_number?.grade || '';
          
          // Extract mandi number from different possible locations
          let mandiNumber = '';
          if (lot?.mandi_number?.mandi_number) {
            mandiNumber = lot.mandi_number.mandi_number;
          } else if (lot?.mandi_number && typeof lot.mandi_number === 'string') {
            mandiNumber = lot.mandi_number;
          } else {
            mandiNumber = `A${lotIndex + 1}`;
          }

          const key = `${variety}-${lotGrade}-${mandiNumber}`;

          if (!groupedData[key]) {
            groupedData[key] = {
              mandiNo: mandiNumber,
              variety,
              lot: lotGrade,
              packType: lot?.pack_type || '',
              grades: {},
              customerShortCode: lot?.customer_short_code || customerCode,
              customerName: lot?.customer_name || customerName,
            };
          }

          // Get the grade name dynamically using mandi_sku_size_id
          let gradeKey = 'Unknown';
          
          if (lot?.mandi_sku_size_id && skuSizesData?.length > 0) {
            const skuSize = skuSizesData.find(sku => sku.id === lot.mandi_sku_size_id);
            if (skuSize) {
              gradeKey = skuSize.size || 'Unknown';
            }
          } else if (lot?.grade) {
            gradeKey = lot.grade;
          }

          if (!groupedData[key].grades[gradeKey]) {
            groupedData[key].grades[gradeKey] = { quantity: 0, price: 0 };
          }

          groupedData[key].grades[gradeKey].quantity += lot?.units || 0;
          groupedData[key].grades[gradeKey].price = Math.max(
            groupedData[key].grades[gradeKey].price,
            lot?.selling_price_per_unit || 0
          );
        });

        const transformedLotData = Object.values(groupedData);

        // Calculate proportional amounts for this customer
        const totalLots = rowData?.lots_data?.length || 1;
        const customerLots = lotsForCustomer.length;
        const proportionalSubTotal = Math.round(
          (rowData?.sub_total || 0) * (customerLots / totalLots)
        );
        const proportionalNetAmount = Math.round(
          (rowData?.net_amount || 0) * (customerLots / totalLots)
        );

        const displaySubTotal =
          totalLots === customerLots
            ? rowData?.sub_total || 0
            : proportionalSubTotal;
        const displayNetAmount =
          totalLots === customerLots
            ? rowData?.net_amount || 0
            : proportionalNetAmount;

        return {
          lotData: transformedLotData,
          totalAmount: displaySubTotal,
          netAmount: displayNetAmount,
          totalExpenses: rowData?.total_expenses || 0,
          customerShortCode: customerCode,
          customerName: customerName,
        };
      });

      const currentMandi = mandiList?.find(m => m.id === mandiId);
      const brandLogo = currentMandi?.brand_url || currentMandi?.logo_url || currentMandi?.mandi_logo || currentMandi?.brand_logo || getBrandLogo();

      const allCustomerSlipsProps = {
        tokenNumber: rowData?.token || '',
        farmerName: rowData?.farmer_name || '',
        date: rowData?.auction_date || new Date().toISOString(),
        brandLogo: brandLogo,
        pageData: pageData,
        skuSizes: skuSizesData || [],
        customerName: pageData?.[0]?.customerName || '', // Pass the first customer's name as fallback
      };

      setCustomerSlipData(allCustomerSlipsProps);
      setShowCustomerSlip(true);
    } catch (error) {
      console.error('Error in generateAllCustomerSlipsInOnePDF:', error);
      NotificationBar('Error generating all customer slips in one PDF', 'error');
    }
  };

  // Handle single mandi selection from popup
  const handleMandiSelection = selectedMandiNumber => {
    setShowMandiSelectionModal(false);
    if (pendingCustomerSlipData) {
      generateCustomerSlipPreview(pendingCustomerSlipData, selectedMandiNumber);
    }
    setPendingCustomerSlipData(null);
  };

  // Handle multiple mandi selection from popup
  const handleMultipleMandiSelection = selectedMandiNumbers => {
    setShowMandiSelectionModal(false);
    if (pendingCustomerSlipData && selectedMandiNumbers.length > 0) {
      generateMergedCustomerSlipPDF(
        pendingCustomerSlipData,
        selectedMandiNumbers
      );
    }
    setPendingCustomerSlipData(null);
  };

  // Generate merged PDF with all selected mandi numbers - 2 mandi numbers per page
  const generateMergedCustomerSlipPDF = (rowData, selectedMandiNumbers) => {
    try {
      // Group lots by mandi number
      const lotsByMandiNumber = {};
      rowData?.lots_data?.forEach((lot, index) => {
        const mandiNumber = lot?.mandi_number?.mandi_number || `A${index + 1}`;
        if (!lotsByMandiNumber[mandiNumber]) {
          lotsByMandiNumber[mandiNumber] = [];
        }
        lotsByMandiNumber[mandiNumber].push(lot);
      });

      // Generate page data for each selected mandi number
      const pageData = selectedMandiNumbers.map(selectedMandiNumber => {
        const lotsForSelectedMandi =
          lotsByMandiNumber[selectedMandiNumber] || [];
      // Helper function to generate data for a single mandi number
      const generateMandiData = (selectedMandiNumber) => {
        const lotsForSelectedMandi = lotsByMandiNumber[selectedMandiNumber] || [];

        const groupedData = {};

        lotsForSelectedMandi.forEach((lot, lotIndex) => {
          const variety = lot?.product_name || '';
          const lotGrade = lot?.mandi_number?.grade || '';
          const key = `${variety}-${lotGrade}`;

          if (!groupedData[key]) {
            // Find pack type information from the first lot of this group
            let packType = '';
            if (lot?.mandi_sku_size_id && skuSizesData?.length > 0) {
              const skuSize = skuSizesData.find(
                sku => sku.id === lot.mandi_sku_size_id
              );
              if (skuSize) {
                packType = skuSize.pack_type || skuSize.packType || '';
              }
            }

            groupedData[key] = {
              variety,
              lot: lotGrade,
              packType: lot?.pack_type || lot?.mandi_pack_name || '', // Use pack_type directly from lot data
              grades: {}, // Dynamic grades will be added as needed
            };
          }

          // Get the grade name dynamically using mandi_sku_size_id
          let gradeKey = 'Unknown';

          if (lot?.mandi_sku_size_id && skuSizesData?.length > 0) {
            // Find the SKU size by ID from the API data
            const skuSize = skuSizesData.find(
              sku => sku.id === lot.mandi_sku_size_id
            );

            if (skuSize) {
              gradeKey = skuSize.size || 'Unknown';
            }
          } else if (lot?.grade) {
            // Fallback to lot.grade if mandi_sku_size_id is not available
            gradeKey = lot.grade;
          }

          // Initialize the grade in our structure if it doesn't exist
          if (!groupedData[key].grades[gradeKey]) {
            groupedData[key].grades[gradeKey] = { quantity: 0, price: 0 };
          }

          // Add quantities and use higher price if multiple entries for same grade
          groupedData[key].grades[gradeKey].quantity += lot?.units || 0;
          groupedData[key].grades[gradeKey].price = Math.max(
            groupedData[key].grades[gradeKey].price,
            lot?.selling_price_per_unit || 0
          );
        });

        const transformedLotData = Object.values(groupedData);

        // Calculate proportional amounts for this mandi if multiple mandis exist
        const totalLots = rowData?.lots_data?.length || 1;
        const selectedMandiLots = lotsForSelectedMandi.length;
        const proportionalSubTotal = Math.round(
          (rowData?.sub_total || 0) * (selectedMandiLots / totalLots)
        );
        const proportionalNetAmount = Math.round(
          (rowData?.net_amount || 0) * (selectedMandiLots / totalLots)
        );

        // Use proportional amounts if multiple mandis, otherwise use full amounts
        const displaySubTotal =
          totalLots === selectedMandiLots
            ? rowData?.sub_total || 0
            : proportionalSubTotal;
        const displayNetAmount =
          totalLots === selectedMandiLots
            ? rowData?.net_amount || 0
            : proportionalNetAmount;

        return {
          mandiNo: selectedMandiNumber,
          lotData: transformedLotData,
          totalAmount: displaySubTotal,
          netAmount: displayNetAmount,
          totalExpenses: rowData?.total_expenses || 0,
          customerShortCode:
            lotsForSelectedMandi?.[0]?.customer_short_code || '', // Add customer_short_code for each page
        };
      };

      // Group mandi numbers in pairs (2 per page)
      const pairedPageData = [];
      for (let i = 0; i < selectedMandiNumbers.length; i += 2) {
        const firstMandi = selectedMandiNumbers[i];
        const secondMandi = selectedMandiNumbers[i + 1]; // This will be undefined for odd numbers

        const pageSlips = [generateMandiData(firstMandi)];
        
        // Add second mandi if it exists
        if (secondMandi) {
          pageSlips.push(generateMandiData(secondMandi));
        }

        pairedPageData.push(pageSlips);
      }

      const currentMandi = mandiList?.find(m => m.id === mandiId);
      const brandLogo =
        currentMandi?.brand_url ||
        currentMandi?.logo_url ||
        currentMandi?.mandi_logo ||
        currentMandi?.brand_logo ||
        getBrandLogo();

      const mergedCustomerSlipProps = {
        tokenNumber: rowData?.token || '',
        farmerName: rowData?.farmer_name || '',
        date: rowData?.auction_date || new Date().toISOString(),
        brandLogo: brandLogo,
        pairedPageData: pairedPageData, // Pass paired pages data (2 slips per page)
        skuSizes: skuSizesData || [], // Pass the SKU sizes data from API
      };

      setCustomerSlipData(mergedCustomerSlipProps);
      setShowCustomerSlip(true);
    } catch (error) {
      console.error('Error in generateMergedCustomerSlipPDF:', error);
      NotificationBar('Error generating merged customer slip PDF', 'error');
    }
  };

  // Handle mandi selection modal close
  const handleMandiSelectionClose = () => {
    setShowMandiSelectionModal(false);
    setPendingCustomerSlipData(null);
  };

  const handlePrintCustomerSlip = useReactToPrint({
    contentRef: customerSlipRef,
    onAfterPrint: () => {
      setShowCustomerSlip(false);
      setCustomerSlipData(null);
    },
    onPrintError: error => {
      console.error('Customer slip print error:', error);
      NotificationBar('Error printing customer slip', 'error');
    },
  });

  const getPaymentRequest = data => {
    const prId = data?.lots_data?.[0]?.payment_request_id || '';
    if (prId)
      getPaymentRequestById(prId).then(
        ({ advance_amount_adjustments = [], ...rest }) => {
          advance_amount_adjustments = advance_amount_adjustments?.map(
            item => ({
              ...item,
              remaining_amount: advancePayment?.find(
                i => item?.from_payment_request_id === i?.id
              )?.remaining_amount,
            })
          );
          setPRData({ ...rest, advance_amount_adjustments });
        }
      );
  };

  const closeModal = (setInitialData = () => {}) => {
    setData({});
    setPRData({});
    setOpen(false);
    setInitialData({
      payment_request_bill: '',
      bill: '',
    });
  };

  const getUpdatedFarmers = query => {
    getFarmers({ q: query }).then(({ items = [] }) => {
      setFarmers(items);
    });
  };

  const columns = TAB_VALUES?.find(key => key[tab]);
  const DATA = columns[tab];

  const submitPayementRequest = async (values, totalExpense) => {
    setLoading(true);

    if (totalExpense < 0) {
      setLoading(false);
      return NotificationBar('Total expense should not be negative', 'error');
    }

    const {
      payment_request_bill,
      bill,
      due_date = '',
      advance_amount_adjustment,
      mandi_flat_charge,
      flat_charges,
      cash_advance_reciept,
      cash_advance_paid,
      has_cash_advance_paid,
    } = values || {};
    const billData = prData?.id ? bill : payment_request_bill?.length;

    if (!billData) {
      setLoading(false);
      return NotificationBar('Bill Upload Required for Raise PR', 'error');
    }

    const fileData = await fileUpload(
      [payment_request_bill?.[payment_request_bill?.length - 1]],
      'defaultUrl',
      true
    );
    const cashAdvanceReciept = await fileUpload([
      cash_advance_reciept?.[cash_advance_reciept?.length - 1],
    ]);

    const date = new Date(due_date);
    const milliseconds = date.getTime();
    const updateData = {
      farmer_token_id: data?.lots_data?.[0]?.farmer_token_id,
      priority: 1,
      due_date: milliseconds,
      bill: fileData?.[0],
      vendor_id: data?.farmer_id,
      auction_id: data?.lots_data?.[0]?.auction_id,
      token: data?.token,
      auction_date: data?.auction_date,
      dcId: +dcId,
      mandi_id: mandiId,
      ...(prData?.purchase_order_id
        ? { purchase_order_id: prData?.purchase_order_id }
        : ''),
      ...(prData?.id ? { payment_request_id: prData?.id } : ''),
      advance_amount_adjustment: advance_amount_adjustment
        ?.filter(({ amount = 0 }) => amount)
        ?.map(({ id = '', amount = 0, from_payment_request_id }) => ({
          from_payment_request_id: from_payment_request_id || id,
          amount,
        })),
      flat_charges:
        prData?.id && flat_charges?.length
          ? flat_charges?.map(({ charge_type, charge_value, id }) => ({
              id,
              charge_type,
              charge_value: +charge_value,
            }))
          : mandi_flat_charge?.map(({ item, charge_value }) => ({
              charge_type: item,
              charge_value: +charge_value || 0,
            })),
      cash_advance_paid: has_cash_advance_paid ? cash_advance_paid : 0,
      cash_advance_reciept: cashAdvanceReciept?.[0] || null,
      has_cash_advance_paid,
    };

    const processPaymentRequest = prData?.id ? updatePR : createPR;
    processPaymentRequest(updateData, prData?.id)
      .then(() => {
        closeModal();
        loadTokenListing({
          currentFilter: filters,
        });
        NotificationBar(
          `Payement Request ${prData?.id ? 'Updated' : 'Created'} successfully.`
        );
      })
      .finally(() => setLoading(false));
  };

  const handleAttachmentSave = async (
    tokenData,
    attachments,
    initiateInstantPayment = false
  ) => {
    try {
      const payload = {
        farmer_token_id: tokenData?.lots_data?.[0]?.farmer_token_id,
        uploads: attachments,
      };

      await updateTokenAttachments(payload);

      // Reload the token listing to reflect the changes
      loadTokenListing({ currentFilter: filters });

      if (initiateInstantPayment) {
        // TODO: Add instant payment logic here
        NotificationBar(
          'Attachments saved and instant payment initiated',
          'success'
        );
      } else {
        NotificationBar('Attachments saved successfully', 'success');
      }
    } catch (error) {
      console.error('Error saving attachments:', error);
      NotificationBar('Failed to save attachments', 'error');
      throw error;
    }
  };

  useEffect(() => {
    const queryObject = queryString.parse(location.search, {
      arrayFormat: 'separator',
      arrayFormatSeparator: '|',
    });
    if (queryObject && Object.keys(queryObject)?.length === 0) {
      navigate(
        `${location.pathname}?${queryString.stringify({ ...initialValues })}`,
        { replace: true }
      );
    }
  }, []);

  useEffect(() => {
    if (prData?.advance_amount_adjustments?.length && advancePayment?.length) {
      const avancePrData = [
        ...prData?.advance_amount_adjustments,
        ...advancePayment,
      ]?.reduce((a, cv) => {
        if (
          !a?.some(
            ({ from_payment_request_id = '' }) =>
              from_payment_request_id === cv.id ||
              from_payment_request_id === cv.from_payment_request_id
          )
        ) {
          a.push(cv);
        }
        return a;
      }, []);
      setAdvancePrData(avancePrData);
    }
  }, [advancePayment, prData]);

  const getColumns = () => {
    const tabValue = tab === PR_RAISED || tab === PR_APPROVED;

    let columns = DATA;
    const isUserApprover =
      userInfo?.id &&
      mandiConfig?.approver_ids?.some(
        approver_id => approver_id === userInfo.id
      );
    if (isUserApprover && tabValue) {
      columns = [
        ...columns,
        {
          header: 'Approver',
          key: 'approver',
          render: ({ rowData, props: { loadTokenListing } }) => {
            const { approved_by, approved_on } = rowData;
            return !approved_by ? (
              <PaymentAction data={rowData} callBack={loadTokenListing} />
            ) : (
              <>
                <Typography>{approved_by}</Typography>
                <Typography>{getFormattedDate(approved_on)}</Typography>
              </>
            );
          },
        },
      ];
    }
    return columns;
  };

  return (
    <PageFilter
      initialValues={initialValues}
      initialApiCall
      filterLabel='Tokens'
      showSelectMandi
      showLiveLink
      setFilters={value => submitFilters(value)}
      data={[
        {
          type: 'fieldCombo',
          name: 'type',
          label: 'Type',
          placeholder: 'Type',
          options: INWARD_TYPE_OPTIONS,
          style: { width: '150px', marginTop: 0 },
          optionLabel: ({ text }) => `${text}`,
          multiple: false,
          onChange: e => setType(e?.value),
          onChangeInput: value => setType(value),
        },
        {
          type: 'fieldInput',
          name: 'token_id',
          label: 'Token Number',
          placeholder: 'Enter Token Number',
          inputType: 'number',
          style: { width: '200px' },
          InputProps: {
            startAdornment: type && `${type}-`,
          },
        },
        {
          type: 'fieldCombo',
          name: 'farmer',
          label: 'Search Farmer',
          placeholder: 'Search Farmer',
          options: farmers,
          style: { width: '200px', marginTop: 0 },
          optionLabel: farmer => `${farmer.name}${`-${farmer.phone_number}`}`,
          onChangeInput: value => getUpdatedFarmers(value),
          groupBy: value => value.type,
          multiple: false,
        },
        {
          type: 'fieldDatepicker',
          name: 'auction_date',
          label: 'Select Auction Date',
          placeholder: 'Select Auction Date',
          style: { width: '200px', marginTop: 0 },
        },
      ]}
    >
      <PageLayout.Body>
        <TabWrapper>
          <Tabs
            value={tab}
            onChange={handleChangeTab}
            indicatorColor='primary'
            textColor='primary'
            variant='scrollable'
            scrollButtons='auto'
            allowScrollButtonsMobile
          >
            {TOKEN_STATUS_LIST.map(({ value, label }) => (
              <Tab
                label={`${label.toUpperCase()} (${counts[value.toLowerCase()] || 0})`}
                key={value}
                value={value}
                sx={{
                  fontWeight: 'bold',
                  textTransform: 'uppercase',
                  '&.Mui-selected': {
                    fontWeight: 'bold',
                  },
                }}
              />
            ))}
          </Tabs>
        </TabWrapper>
        {loading ? (
          <AppLoader />
        ) : Object.values(tokenList)?.length ? (
          <Table
            data={Object.values(tokenList || [])}
            columns={getColumns()}
            header={true}
            cellProps={() => ({
              handleFarmerBill,
              openModal,
              openBreakDownModalFnc,
              handlePreview,
              loadTokenListing,
              handleModalToggle,
              modalOpen,
              undoData,
              filters,
              tab,
              setRecordArrival,
              recordArrival,
              onDownloadPriceSlip: handleDownloadPriceSlip,
              onDownloadCustomerSlip: handleDownloadCustomerSlip,
              onAttachmentSave: handleAttachmentSave,
            })}
          />
        ) : (
          <NoDataAvailable />
        )}
      </PageLayout.Body>
      {totalCount > 1 && (
        <CustomPagination
          count={totalCount}
          page={page}
          shape='circular'
          onChange={handleChangePage}
        />
      )}
      <PaymentRequestModal
        open={open}
        closeModal={closeModal}
        data={data}
        prData={prData}
        submitPayementRequest={submitPayementRequest}
        payment_term_days={mandiConfig?.payment_term_days}
        loading={loading}
        advance_amount_adjustment={
          prData?.advance_amount_adjustments?.length
            ? advancePrData
            : advancePayment
        }
        otherCharges={footerValue}
        mandi={mandi}
        flat_charges={footerValue?.flat_charges}
        farmer_material_charges={footerValue?.farmer_material_charges}
        tab={tab}
      />
      {openBreakDownModal && (
        <ValueBreakDownModal
          open={openBreakDownModal}
          closeModal={() => setOpenBreakDownModal(false)}
          data={lotsData}
          showCustomerName
          showUnloading={lotsData.inward_type === INWARD_TYPE.CRATES}
          footerValue={footerValue}
          mandiCharges={mandi?.farmer_flat_charges}
          loading={breakDownModalLoading}
        />
      )}
      {!!showBill && (
        <FarmerBill
          ref={supplyBillRef}
          showDialog={showBill}
          toggleShowBill={toggleShowBill}
          handlePrintHelper={handlePrint}
          mandiId={mandiId}
          auctionDate={rowData?.auction_date}
          inwardType={rowData.inward_type}
          token={rowData?.token}
        />
      )}
      <PrintTokenSlipWrapper
        open={showPriceSlipPreview}
        onClose={togglePriceSlipPreview}
        priceSlipData={priceSlipData}
      />
      {showCustomerSlip && customerSlipData && (
        <CustomModal
          title='Customer Slip Preview'
          open={showCustomerSlip}
          onClose={() => setShowCustomerSlip(false)}
          maxWidth='md'
          fullWidth
          dataCy={{ 'data-cy': 'mandi.customerSlip.previewModal' }}
          footerComponent={
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                padding: '16px',
              }}
            >
              <Button
                color='primary'
                size='medium'
                onClick={handlePrintCustomerSlip}
                variant='contained'
                data-cy='mandi.customerSlip.printButton'
              >
                Print
              </Button>
            </div>
          }
        >
          <div ref={customerSlipRef}>
            <PrintCustomerSlip {...customerSlipData} />
          </div>
        </CustomModal>
      )}
      <MandiSelectionModal
        open={showMandiSelectionModal}
        onClose={handleMandiSelectionClose}
        mandiNumbers={
          pendingCustomerSlipData
            ? Object.keys(
                pendingCustomerSlipData?.lots_data?.reduce(
                  (acc, lot, index) => {
                    const mandiNumber =
                      lot?.mandi_number?.mandi_number || `A${index + 1}`;
                    acc[mandiNumber] = true;
                    return acc;
                  },
                  {}
                ) || {}
              )
            : []
        }
        onSelectMandi={handleMandiSelection}
        onSelectMultiple={handleMultipleMandiSelection}
      />
    </PageFilter>
  );
};

export default TokenPage;

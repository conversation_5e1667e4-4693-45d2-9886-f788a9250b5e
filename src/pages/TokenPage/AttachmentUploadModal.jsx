import React, { useState } from 'react';

import { Button, <PERSON>, Typography, Grid, CircularProgress } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';

import { Modal as CustomModal } from 'Components';
import UploadInput from 'Components/FormFields/UploadInput';
import ImageList from 'Components/ImageList';
import useNotify from 'Hooks/useNotify';
import fileUpload from 'Utilities/fileUpload';

const validationSchema = Yup.object().shape({
  attachments: Yup.array()
    .min(1, 'At least one attachment is required')
    .required('Attachments are required'),
});

const AttachmentUploadModal = ({
  open,
  onClose,
  tokenData,
  onSave,
  existingAttachments = [],
}) => {
  const [loading, setLoading] = useState(false);
  const NotificationBar = useNotify();

  const initialValues = {
    attachments: existingAttachments.map(att => att?.url || att).filter(Boolean),
  };

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      // Upload files if any new ones are added
      const uploadedFiles = await fileUpload(
        values.attachments.filter(file => file instanceof File),
        'mandi',
        true
      );

      // Combine existing attachments with new uploads
      const existingIds = existingAttachments.map(att => att?.id).filter(Boolean);
      const allAttachments = [
        ...existingIds,
        ...uploadedFiles,
      ];

      // Call the save function with the token data and attachments
      await onSave(tokenData, allAttachments);
      
      NotificationBar('Attachments uploaded successfully', 'success');
      onClose();
    } catch (error) {
      console.error('Error uploading attachments:', error);
      NotificationBar('Failed to upload attachments', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleInstantPayment = async (values) => {
    setLoading(true);
    try {
      // Upload files if any new ones are added
      const uploadedFiles = await fileUpload(
        values.attachments.filter(file => file instanceof File),
        'mandi',
        true
      );

      // Combine existing attachments with new uploads
      const existingIds = existingAttachments.map(att => att?.id).filter(Boolean);
      const allAttachments = [
        ...existingIds,
        ...uploadedFiles,
      ];

      // Call the save function with instant payment flag
      await onSave(tokenData, allAttachments, true);
      
      NotificationBar('Attachments uploaded and instant payment initiated', 'success');
      onClose();
    } catch (error) {
      console.error('Error uploading attachments:', error);
      NotificationBar('Failed to upload attachments', 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <CustomModal
      open={open}
      onClose={onClose}
      title={`${existingAttachments.length > 0 ? 'Edit' : 'Upload'} Attachment - Token: ${tokenData?.token || ''}`}
      contentSize
      screenWidth="500px"
    >
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {({ values, isValid }) => (
          <Form>
            <Box p={2}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Box mb={2}>
                    <Typography variant="h6" gutterBottom>
                      Upload Attachments
                    </Typography>
                    <UploadInput
                      accept="image/*, application/pdf"
                      label="Attach Files"
                      name="attachments"
                      multiple
                      size="small"
                    />

                    {values.attachments?.length > 0 && (
                      <Box mt={2}>
                        <Typography variant="h6" gutterBottom>
                          Attached Files
                        </Typography>
                        <ImageList name="attachments" showClose />
                      </Box>
                    )}
                  </Box>
                </Grid>
              </Grid>
            </Box>

            <Box
              p={2}
              display="flex"
              justifyContent="space-between"
              borderTop="1px solid #e0e0e0"
            >
              <Button
                variant="outlined"
                onClick={onClose}
                disabled={loading}
              >
                Cancel
              </Button>
              
              <Button
                variant="contained"
                disabled={!isValid || loading}
                color="primary"
                onClick={() => handleInstantPayment(values)}
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
              >
                {loading ? 'Processing...' : 'Save and Initiate Instant Payment'}
              </Button>
            </Box>
          </Form>
        )}
      </Formik>
    </CustomModal>
  );
};

export default AttachmentUploadModal;

import { SettingsBackupRestore as SettingsBackupRestoreIcon } from '@mui/icons-material';
import { Button, Grid, Typography } from '@mui/material';

import { ImageThumb } from 'Components';
import ImageIcons from 'Components/AppIcons/ImageIcons.jsx';
import CancelTokenModal from 'Pages/GateIn/CancelTokenModal';
import { MANDI_TYPE } from 'Utilities/constants/lots';
import { getFormattedDate } from 'Utilities/dateUtils';
import { getPartnerStatus } from 'Utilities/lots';

import AttachmentColumn from './AttachmentColumn';
import { PartnerStatusWrapper } from './Styled';
import {
  ActionCol,
  FarmerCol,
  ProductCol,
  TokenCol,
  TotalValuesCol,
  UnitQuantityCol,
} from './tokenColumnsComponent';

const getPartnerStatusUI = rowData => {
  const { bank_details, kyc_details, otp_details, upi_details } = rowData;

  const bank_icon = `bank${getPartnerStatus(bank_details.status)}`;
  const kyc_icon = `kyc${getPartnerStatus(kyc_details.status)}`;
  const otp_icon = `otp${getPartnerStatus(otp_details.status)}`;
  const upi_icon = `upi${getPartnerStatus(upi_details.status)}`;

  return (
    <PartnerStatusWrapper>
      {!!kyc_details.status && <ImageIcons name={kyc_icon} alt='kycIcon' />}
      {!!bank_details.status && <ImageIcons name={bank_icon} alt='bankIcon' />}
      {!!otp_details.status && <ImageIcons name={otp_icon} alt='otpIcon' />}
      {!!upi_details.status && <ImageIcons name={upi_icon} alt='upiIcon' />}
    </PartnerStatusWrapper>
  );
};

export const COLUMNS = [
  {
    header: 'Token',
    key: 'token',
    style: { width: '9rem' },
    render: ({
      data,
      rowData: {
        gatein_id = '',
        auction_date = '',
        inward_type = '',
        received_satellite_cc_name,
      },
      rowData,
      props: { tab = '', onDownloadPriceSlip, onDownloadCustomerSlip },
    }) => (
      <TokenCol
        tab={tab}
        data={data}
        gatein_id={gatein_id}
        auction_date={auction_date}
        inward_type={inward_type}
        mandi_type={MANDI_TYPE.MAIN_MANDI}
        satellite_name={received_satellite_cc_name}
        rowData={rowData}
        onDownloadPriceSlip={onDownloadPriceSlip}
        onDownloadCustomerSlip={onDownloadCustomerSlip}
      />
    ),
  },
  {
    header: 'Farmer Name and Address',
    key: 'farmer_name',
    style: { width: '20rem' },
    render: ({ rowData }) => (
      <FarmerCol rowData={rowData} getPartnerStatusUI={getPartnerStatusUI} />
    ),
  },
  {
    header: 'Product',
    key: 'product',
    render: ({
      rowData: {
        auction_date = '',
        lots_data = [],
        inward_type = '',
        token_suffix = '',
      },
      props: { tab = '' },
    }) => (
      <ProductCol
        tab={tab}
        auction_date={auction_date}
        lots_data={lots_data}
        inward_type={inward_type}
        token_suffix={token_suffix}
      />
    ),
  },
];

export const AUCTION_READY = [
  {
    header: 'Token',
    key: 'token',
    style: { width: '9rem' },
    render: ({
      data,
      rowData: {
        gatein_id = '',
        auction_date = '',
        inward_type = '',
        received_satellite_cc_name = '',
      },
      rowData,
      props: { tab = '', onDownloadPriceSlip, onDownloadCustomerSlip },
    }) => (
      <TokenCol
        tab={tab}
        data={data}
        gatein_id={gatein_id}
        auction_date={auction_date}
        inward_type={inward_type}
        mandi_type={MANDI_TYPE.MAIN_MANDI}
        satellite_name={received_satellite_cc_name}
        rowData={rowData}
        onDownloadPriceSlip={onDownloadPriceSlip}
        onDownloadCustomerSlip={onDownloadCustomerSlip}
      />
    ),
  },
  {
    header: 'Farmer Name and Address',
    key: 'farmer_name',
    style: { width: '20rem' },
    render: ({ rowData }) => (
      <FarmerCol rowData={rowData} getPartnerStatusUI={getPartnerStatusUI} />
    ),
  },
  {
    header: 'Product',
    key: 'product_auction',
    render: ({
      rowData: {
        lots_data = [],
        auction_date,
        inward_type = '',
        token_suffix = '',
      },
      props: { tab = '' },
    }) => (
      <ProductCol
        tab={tab}
        auction_date={auction_date}
        lots_data={lots_data}
        inward_type={inward_type}
        token_suffix={token_suffix}
      />
    ),
  },
  {
    header: 'Unit/Quantity',
    key: 'unit_quantity_auction',
    render: ({
      rowData: { lots_data, inward_type = '' },
      props: { tab = '' },
    }) => (
      <UnitQuantityCol
        tab={tab}
        lots_data={lots_data}
        inward_type={inward_type}
      />
    ),
  },
];

export const SOLD = [
  {
    header: 'Token',
    key: 'token',
    style: { width: '8rem' },
    render: ({
      data,
      rowData: {
        inward_type = '',
        gatein_id = '',
        auction_date = '',
        received_satellite_cc_name = '',
      },
      rowData,
      props: { tab = '', onDownloadPriceSlip, onDownloadCustomerSlip },
    }) => (
      <TokenCol
        tab={tab}
        data={data}
        gatein_id={gatein_id}
        auction_date={auction_date}
        inward_type={inward_type}
        mandi_type={MANDI_TYPE.MAIN_MANDI}
        satellite_name={received_satellite_cc_name}
        rowData={rowData}
        onDownloadPriceSlip={onDownloadPriceSlip}
        onDownloadCustomerSlip={onDownloadCustomerSlip}
      />
    ),
  },
  {
    header: 'Farmer Name and Address',
    key: 'farmer_name',
    render: ({ rowData }) => (
      <FarmerCol rowData={rowData} getPartnerStatusUI={getPartnerStatusUI} />
    ),
  },
  {
    header: 'Product',
    key: 'product_sold',
    render: ({ rowData: { lots_data, inward_type }, props: { tab = '' } }) => (
      <ProductCol tab={tab} lots_data={lots_data} inward_type={inward_type} />
    ),
  },
  {
    header: 'Unit/Quantity',
    key: 'unit_quantity_sold',
    render: ({
      rowData: { lots_data, auction_date, token_suffix, inward_type = '' },
      props: { tab = '' },
    }) => (
      <UnitQuantityCol
        tab={tab}
        lots_data={lots_data}
        auction_date={auction_date}
        token_suffix={token_suffix}
        inward_type={inward_type}
      />
    ),
  },
  {
    header: 'Total Value',
    key: 'total_value_sold',
    render: ({
      rowData,
      props: {
        openModal = () => {},
        openBreakDownModalFnc = () => {},
        handleFarmerBill = () => {},
        tab = '',
      },
    }) => (
      <TotalValuesCol
        tab={tab}
        rowData={rowData}
        openModal={openModal}
        openBreakDownModalFnc={openBreakDownModalFnc}
        handleFarmerBill={handleFarmerBill}
      />
    ),
  },
  {
    header: 'Attachment',
    key: 'upload',
    render: ({ rowData, props: { tab = '', onAttachmentSave = () => {} } }) => (
      <AttachmentColumn
        rowData={rowData}
        onAttachmentSave={onAttachmentSave}
        tab={tab}
      />
    ),
  },
];

export const PR_RAISED = [
  {
    header: 'Token',
    key: 'token',
    render: ({
      data,
      rowData: {
        inward_type = '',
        auction_date = '',
        received_satellite_cc_name,
      },
      rowData,
      props: { tab = '', onDownloadPriceSlip, onDownloadCustomerSlip },
    }) => (
      <TokenCol
        tab={tab}
        data={data}
        auction_date={auction_date}
        inward_type={inward_type}
        satellite_name={received_satellite_cc_name}
        rowData={rowData}
        onDownloadPriceSlip={onDownloadPriceSlip}
        onDownloadCustomerSlip={onDownloadCustomerSlip}
      />
    ),
  },
  {
    header: 'Farmer Name and Address',
    key: 'farmer_name',
    style: { minWidth: '4rem' },
    render: ({ rowData }) => (
      <FarmerCol rowData={rowData} getPartnerStatusUI={getPartnerStatusUI} />
    ),
  },
  {
    header: 'Product',
    key: 'product_pr',
    render: ({ rowData: { lots_data }, props: { tab = '' } }) => (
      <ProductCol tab={tab} lots_data={lots_data} />
    ),
  },
  {
    header: 'Unit/Quantity',
    key: 'unit_quantity_pr',
    render: ({ rowData: { lots_data = '' }, props: { tab = '' } }) => (
      <UnitQuantityCol tab={tab} lots_data={lots_data} />
    ),
  },
  {
    header: 'Total Value',
    key: 'total_value_pr',
    render: ({
      rowData,
      props: {
        openBreakDownModalFnc = () => {},
        openModal = () => {},
        handleFarmerBill = () => {},
        tab = '',
      },
    }) => (
      <TotalValuesCol
        tab={tab}
        rowData={rowData}
        openModal={openModal}
        openBreakDownModalFnc={openBreakDownModalFnc}
        handleFarmerBill={handleFarmerBill}
      />
    ),
  },
];

export const PAID = [
  {
    header: 'Token',
    key: 'token',
    style: { width: '9rem' },
    render: ({
      data,
      rowData: {
        inward_type = '',
        auction_date = '',
        received_satellite_cc_name = '',
      },
      rowData,
      props: { onDownloadPriceSlip, onDownloadCustomerSlip },
    }) => (
      <TokenCol
        tab='Paid'
        data={data}
        auction_date={auction_date}
        inward_type={inward_type}
        satellite_name={received_satellite_cc_name}
        rowData={rowData}
        onDownloadPriceSlip={onDownloadPriceSlip}
        onDownloadCustomerSlip={onDownloadCustomerSlip}
      />
    ),
  },
  {
    header: 'Farmer Name and Address',
    key: 'farmer_name',
    style: { minWidth: '4rem' },
    render: ({ rowData }) => (
      <FarmerCol rowData={rowData} getPartnerStatusUI={getPartnerStatusUI} />
    ),
  },
  {
    header: 'Product',
    key: 'product_paid',
    render: ({ rowData: { lots_data }, props: { tab = '' } }) => (
      <ProductCol tab={tab} lots_data={lots_data} />
    ),
  },
  {
    header: 'Unit/Quantity',
    key: 'unit_quantity_paid',
    render: ({ rowData: { lots_data = '' }, props: { tab = '' } }) => (
      <UnitQuantityCol tab={tab} lots_data={lots_data} />
    ),
  },
  {
    header: 'Total Value',
    key: 'total_value_paid',
    render: ({
      rowData,
      props: {
        openBreakDownModalFnc = () => {},
        handleFarmerBill = () => {},
        tab = '',
      },
    }) => (
      <TotalValuesCol
        tab={tab}
        rowData={rowData}
        openBreakDownModalFnc={openBreakDownModalFnc}
        handleFarmerBill={handleFarmerBill}
      />
    ),
  },
];

export const PR_APPROVED = [
  {
    header: 'Token',
    key: 'token',
    props: { md: 2, xs: 12 },
    render: ({
      data,
      rowData: {
        inward_type = '',
        auction_date = '',
        received_satellite_cc_name = '',
      },
      rowData,
      props: { onDownloadPriceSlip, onDownloadCustomerSlip },
    }) => (
      <TokenCol
        tab='PR_Approved'
        data={data}
        auction_date={auction_date}
        inward_type={inward_type}
        satellite_name={received_satellite_cc_name}
        rowData={rowData}
        onDownloadPriceSlip={onDownloadPriceSlip}
        onDownloadCustomerSlip={onDownloadCustomerSlip}
      />
    ),
  },
  {
    header: 'Farmer Name and Address',
    key: 'farmer_name',
    style: { minWidth: '4rem' },
    render: ({ rowData }) => (
      <FarmerCol rowData={rowData} getPartnerStatusUI={getPartnerStatusUI} />
    ),
  },
  {
    header: 'Product',
    key: 'product_pr_approved',
    render: ({ rowData: { lots_data }, props: { tab = '' } }) => (
      <ProductCol tab={tab} lots_data={lots_data} />
    ),
  },
  {
    header: 'Unit/Quantity',
    key: 'unit_quantity_pr_approved',
    render: ({ rowData: { lots_data = '' }, props: { tab = '' } }) => (
      <UnitQuantityCol tab={tab} lots_data={lots_data} />
    ),
  },
  {
    header: 'Total Value',
    key: 'total_value_pr_approved',
    render: ({
      rowData,
      props: {
        openBreakDownModalFnc = () => {},
        handleFarmerBill = () => {},
        tab = '',
      },
    }) => (
      <TotalValuesCol
        tab={tab}
        rowData={rowData}
        openBreakDownModalFnc={openBreakDownModalFnc}
        handleFarmerBill={handleFarmerBill}
      />
    ),
  },
];

export const CANCELLED = [
  {
    header: 'Token',
    key: 'token',
    style: { width: '9rem' },
    render: ({
      data,
      rowData: {
        auction_date = '',
        inward_type = '',
        received_satellite_cc_name = '',
      },
      rowData,
      props: { tab = '' },
    }) => (
      <TokenCol
        tab={tab}
        data={data}
        auction_date={auction_date}
        inward_type={inward_type}
        satellite_name={received_satellite_cc_name}
        rowData={rowData}
      />
    ),
  },
  {
    header: 'Farmer Name and Address',
    key: 'farmer_name',
    style: { width: '20rem' },
    render: ({ rowData }) => (
      <FarmerCol rowData={rowData} getPartnerStatusUI={getPartnerStatusUI} />
    ),
  },
  {
    header: 'Product',
    key: 'product_cancelled',
    render: ({
      rowData: { lots_data = [], inward_type = '' },
      props: { tab = '' },
    }) => (
      <ProductCol tab={tab} lots_data={lots_data} inward_type={inward_type} />
    ),
  },
  {
    header: 'Reason',
    key: 'reason',
    render: ({
      rowData,
      rowData: { cancellation_reason, comment, lots_data },
      props: {
        modalOpen,
        handleModalToggle = () => {},
        loadTokenListing,
        undoData,
        filters,
      },
    }) => (
      <Grid container style={{ alignItems: 'center' }}>
        <Grid item md={6}>
          <Typography>{cancellation_reason}</Typography>
          <Typography>Comment: {comment}</Typography>
        </Grid>
        {lots_data && lots_data[0] && !lots_data[0].is_farmer_return && (
          <Grid item md={6}>
            <Button
              variant='outlined'
              onClick={() => handleModalToggle(rowData)}
            >
              Undo&nbsp;
              <SettingsBackupRestoreIcon />
            </Button>
          </Grid>
        )}
        <CancelTokenModal
          data={undoData}
          open={modalOpen}
          toggleModal={handleModalToggle}
          loadTokenListing={loadTokenListing}
          currentFilter={filters}
        />
      </Grid>
    ),
  },
];

export const SELF_GATEIN = [
  {
    header: 'Token',
    key: 'token',
    style: { width: '9rem' },
    render: ({
      data,
      rowData: {
        auction_date = '',
        inward_type = '',
        gatein_id = '',
        received_satellite_cc_name = '',
      },
      rowData,
      props: { tab = '' },
    }) => (
      <TokenCol
        tab={tab}
        data={data}
        gatein_id={gatein_id}
        auction_date={auction_date}
        inward_type={inward_type}
        satellite_name={received_satellite_cc_name}
        rowData={rowData}
      />
    ),
  },
  {
    header: 'Farmer Name and Address',
    key: 'farmer_name',
    style: { width: '20rem' },
    render: ({ rowData }) => (
      <FarmerCol rowData={rowData} getPartnerStatusUI={getPartnerStatusUI} />
    ),
  },
  {
    header: 'Product',
    key: 'product_self_gatein',
    render: ({ rowData: { lots_data = [] }, props: { tab = '' } }) => (
      <ProductCol tab={tab} lots_data={lots_data} />
    ),
  },
  {
    header: 'Unit/Quantity',
    key: 'unit_quantity_self_gatein',
    render: ({ rowData: { lots_data }, props: { tab = '' } }) => (
      <UnitQuantityCol tab={tab} lots_data={lots_data} />
    ),
  },
  {
    header: 'Total Value',
    key: 'total_value_self_gatein',
    render: ({
      rowData,
      props: {
        openModal = () => {},
        openBreakDownModalFnc = () => {},
        handleFarmerBill = () => {},
        tab = '',
      },
    }) => (
      <TotalValuesCol
        tab={tab}
        rowData={rowData}
        openModal={openModal}
        openBreakDownModalFnc={openBreakDownModalFnc}
        handleFarmerBill={handleFarmerBill}
      />
    ),
  },
  {
    header: 'Attachment',
    key: 'upload',
    render: ({ rowData: { uploads = [] } }) =>
      uploads && <ImageThumb file={uploads[0]?.url} url={uploads[0]?.url} />,
  },
];

export const IN_SATELLITE_MANDI = [
  {
    header: 'Token',
    key: 'token',
    render: ({
      data,
      rowData: {
        gatein_id = '',
        auction_date = '',
        inward_type = '',
        received_satellite_cc_name = '',
      },
      rowData,
      props: { tab = '' },
    }) => (
      <TokenCol
        tab={tab}
        data={data}
        gatein_id={gatein_id}
        auction_date={auction_date}
        inward_type={inward_type}
        mandi_type={MANDI_TYPE.SATELLITE}
        satellite_name={received_satellite_cc_name}
        rowData={rowData}
      />
    ),
  },
  {
    header: 'Farmer Name and Address',
    key: 'farmer_name',
    render: ({ rowData }) => (
      <FarmerCol rowData={rowData} getPartnerStatusUI={getPartnerStatusUI} />
    ),
  },
  {
    header: 'Product',
    key: 'product_satellite_mandi',
    render: ({
      rowData: {
        auction_date = '',
        lots_data = [],
        inward_type = '',
        token_suffix = '',
      },
      props: { tab = '' },
    }) => (
      <ProductCol
        tab={tab}
        auction_date={auction_date}
        lots_data={lots_data}
        inward_type={inward_type}
        token_suffix={token_suffix}
      />
    ),
  },
  {
    header: 'Action',
    key: 'action',
    render: ({
      rowData: { lots_data = [] },
      props: { setRecordArrival = () => {}, recordArrival },
    }) => {
      const farmerTokenId = lots_data[0]?.farmer_token_id;
      return (
        <ActionCol
          farmerTokenId={farmerTokenId}
          setRecordArrival={setRecordArrival}
          recordArrival={recordArrival}
        />
      );
    },
  },
];

export const SCHEDULED_TO_PAY = [
  {
    header: 'Token',
    key: 'token',
    style: { width: '9rem' },
    render: ({
      data,
      rowData: {
        inward_type = '',
        auction_date = '',
        received_satellite_cc_name = '',
      },
      rowData,
      props: { onDownloadPriceSlip, onDownloadCustomerSlip },
    }) => (
      <TokenCol
        tab='scheduled_to_pay'
        data={data}
        auction_date={auction_date}
        inward_type={inward_type}
        satellite_name={received_satellite_cc_name}
        rowData={rowData}
        onDownloadPriceSlip={onDownloadPriceSlip}
        onDownloadCustomerSlip={onDownloadCustomerSlip}
      />
    ),
  },
  {
    header: 'Farmer Name and Address',
    key: 'farmer_name',
    style: { minWidth: '4rem' },
    render: ({ rowData }) => (
      <FarmerCol rowData={rowData} getPartnerStatusUI={getPartnerStatusUI} />
    ),
  },
  {
    header: 'Product',
    key: 'product_scheduled_pay',
    render: ({ rowData: { lots_data }, props: { tab = '' } }) => (
      <ProductCol tab={tab} lots_data={lots_data} />
    ),
  },
  {
    header: 'Unit/Quantity',
    key: 'unit_quantity_scheduled_pay',
    render: ({ rowData: { lots_data = '' }, props: { tab = '' } }) => (
      <UnitQuantityCol tab={tab} lots_data={lots_data} />
    ),
  },
  {
    header: 'Total Value',
    key: 'total_value_scheduled_pay',
    render: ({
      rowData,
      props: {
        openBreakDownModalFnc = () => {},
        handleFarmerBill = () => {},
        tab = '',
      },
    }) => (
      <TotalValuesCol
        tab={tab}
        rowData={rowData}
        openBreakDownModalFnc={openBreakDownModalFnc}
        handleFarmerBill={handleFarmerBill}
      />
    ),
  },
  {
    header: 'Scheduled On',
    key: 'scheduled_on',
    render: ({ rowData }) => {
      const { scheduled_on } = rowData;
      return <Typography>{getFormattedDate(scheduled_on)}</Typography>;
    },
  },
];

export const COLUMN_ITEM = [
  {
    key: 'packaging_type_charge',
    header: 'Items',
    footer: 'total',
  },
  {
    key: 'amount',
    header: '',
    footer: 'totalAmount',
  },
];

export const COLUMNS_PACK = [
  {
    key: 'mandi_packaging_type',
    header: 'Pack Type',
    footer: 'total',
  },
  {
    key: 'units',
    header: 'Unit',
    footer: 'totalUnit',
  },
];

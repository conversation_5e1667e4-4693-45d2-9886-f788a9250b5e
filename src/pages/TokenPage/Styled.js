import { Grid, Tooltip } from '@mui/material';
import { makeStyles, withStyles } from '@mui/styles';
import styled from 'styled-components';

export const ButtonWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
  margin: 8px 0;
`;

export const TabWrapper = styled.div`
  margin-bottom: 1rem;

  ${props => props.theme.breakpoints.down('md')} {
    .MuiTabs-fixed {
      max-width: 745px;
      overflow-x: scroll !important;

      .MuiTab-root {
        min-width: 145px;
      }
    }
  }
`;

export const RecordGridContainer = styled(Grid)`
  padding: ${props => props.theme.spacing(1)};
  & > div {
    width: 100%;
    margin: ${props => props.theme.spacing(1)};
  }
`;

export const ImageListWrapper = styled.div`
  display: flex;
  padding-bottom: ${props => props.theme.spacing(0.5)};
  overflow-x: auto;
  span {
    position: relative;
    display: flex;
    margin-right: ${props => props.theme.spacing(1)};
    padding: ${props => props.theme.spacing(1)};
    background: #fff;
    border-radius: 4px;
    img {
      width: 100px;
    }
    .cancel-icon {
      position: absolute;
      top: 1px;
      right: -2px;
    }
    .pdf-icon {
      font-size: ${props => props.theme.typography.h2.fontSize};
    }
  }
`;

export const PartnerStatusWrapper = styled.div`
  display: flex;
  gap: 0.5rem;
`;

export const UnloadingData = styled.div`
  display: flex;
  background-color: #e0e0e0;
  max-width: 5.5rem;
  width: auto;
  padding: 0 2px;
  border-radius: 4px;
`;

export const CustomTooltip = withStyles({
  tooltip: {
    color: 'black',
    backgroundColor: 'white',
    boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.25)',
  },
  arrow: {
    '&:before': {
      boxShadow: '0px 0px 2px rgba(0, 0, 0, 0.25)',
    },
    color: 'white',
  },
})(Tooltip);

export const useStyles = makeStyles(() => ({
  container: {
    marginLeft: '1rem',
  },
  total: {
    margin: '.5rem 0 0 .5rem',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  subCharge: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 1,
  },
  commision: {
    display: 'flex',
    alignItems: 'center',
    gap: 1,
    justifyContent: 'space-between',
  },
}));

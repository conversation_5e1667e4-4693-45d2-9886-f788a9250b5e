import React, { useEffect } from 'react';

import {
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  InputAdornment,
  Typography,
  Box,
} from '@mui/material';
import { FieldArray, useFormikContext } from 'formik';
import { debounce } from 'lodash';

import { AppButton, ImageThumb } from 'Components';
import { FieldCombo, FieldInput, UploadInput } from 'Components/FormFields';
import { ImageUploadButton } from 'Components/ImageUploadModal';
import FarmerDetails from 'Pages/GateIn/FarmerDetails';
import { mergeValidator } from 'Utilities';
import { INWARD_TYPE } from 'Utilities/constants';
import {
  validateFileUpload,
  validateMin,
  validatePhone,
  validateRequired,
} from 'Utilities/formvalidation';

import { ImageListWrapper, ItemGridContainer } from './styled';

const GateInDetails = props => {
  const {
    transporters,
    getTransporters,
    setOpenTransporterModal,
    removeAttachments,
    gatein_id,
    products,
    skus,
    isGrossRequired,
    marginTop6,
    saveImages,
    setGateinPhotos,
    handleUpload,
    upload,
    skuSizes,
    packTypes,
    onDownloadPriceSlip,
    PHSlipUploadMandatory,
  } = props;
  const { values, setFieldValue } = useFormikContext();

  useEffect(() => {
    if (!location.pathname.includes('edit')) {
      setFieldValue('vehicle_number', null);
      setFieldValue('driver_phone_number', null);
    }
  }, [location.pathname]);

  return (
    <div>
      <ItemGridContainer container direction='row' spacing={0}>
        {!!(values.inwards === INWARD_TYPE.CRATES) && (
          <Grid>
            <FieldCombo
              name='transporter'
              label='Transporter'
              placeholder='Search Transporter'
              variant='outlined'
              size='small'
              options={transporters || []}
              onChangeInput={debounce(q => getTransporters({ q }), 300)}
              optionLabel={({ name = '', phone_number = '' }) =>
                `${name}-${phone_number}`
              }
              InputLabelProps={{
                required: true,
                shrink: true,
              }}
              inputProps={{ 'data-cy': 'mandi.gateIn.selectTransporter' }}
            />
            <Box sx={{ mt: 1 }}>
              <Button
                variant='outlined'
                color='primary'
                size='small'
                onClick={() => setOpenTransporterModal(true)}
                data-cy='mandi.gateIn.addTransporter'
                sx={{
                  borderColor: 'primary.light',
                  color: 'primary.main',
                  backgroundColor: 'transparent',
                  textTransform: 'none',
                  fontWeight: 500,
                  borderRadius: 2,
                  px: 2,
                  py: 0.5,
                  '&:hover': {
                    borderColor: 'primary.main',
                    backgroundColor: 'primary.light',
                    color: 'primary.contrastText',
                  },
                  transition: 'all 0.2s ease-in-out',
                }}
              >
                + Add Transporter
              </Button>
            </Box>
          </Grid>
        )}
        <FieldInput
          name='vehicle_number'
          size='small'
          placeholder='MH99AB9999'
          variant='outlined'
          label='Vehicle Number'
          style={{ maxWidth: '30%', width: '240px' }}
          InputLabelProps={{
            shrink: true,
          }}
          validate={values.transporter && validateRequired}
          inputProps={{
            maxLength: 10,
            style: { textTransform: 'uppercase' },
            'data-cy': 'mandi.gateIn.vehicleNumber',
          }}
        />
        <FieldInput
          name='driver_phone_number'
          size='small'
          label='Driver Phone Number'
          variant='outlined'
          style={{ maxWidth: '30%', width: '240px' }}
          validate={mergeValidator(
            values.transporter && validateRequired,
            validatePhone
          )}
          InputLabelProps={{
            shrink: true,
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position='start'>+91</InputAdornment>
            ),
          }}
          inputProps={{
            maxLength: 10,
            'data-cy': 'mandi.gateIn.driverPhoneNumber',
          }}
        />
        {!!values.transporter && (
          <Grid
            item
            style={{ display: 'flex', flexDirection: 'column' }}
            justifyContent='right'
          >
            <FieldInput
              name='transporter_amount'
              size='small'
              label='Transporter Amount'
              placeholder='Enter Amount'
              variant='outlined'
              type='number'
              validate={mergeValidator(validateRequired, validateMin(0))}
              style={{ marginBottom: '0.3rem' }}
              InputLabelProps={{
                required: true,
                shrink: true,
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position='start'>₹</InputAdornment>
                ),
              }}
              inputProps={{
                'data-cy': 'mandi.gateIn.transporterAmount',
              }}
            />
            <div style={{ display: 'flex' }}>
              <UploadInput
                accept='image/*, application/pdf'
                name='transporter_bill_photos'
                label='Transporter Bill'
                multiple
                required
                validate={validateFileUpload}
                dataCy={{
                  'data-cy': 'mandi.gateIn.transporterBillPhotos.upload',
                }}
              />
            </div>
            {(() => {
              const photos = values?.transporter_bill_photos;
              if (!photos) return null;

              // Convert object format {id: url} to array format
              const photosArray = Array.isArray(photos)
                ? photos
                : Object.entries(photos).map(([id, url]) => ({ id, url }));

              if (photosArray.length === 0) return null;

              return (
                <div style={{ marginTop: '0.5rem' }}>
                  <div
                    style={{
                      display: 'grid',
                      gridTemplateColumns: 'repeat(2, 100px)',
                      gap: '0.5rem',
                      maxWidth: '210px',
                    }}
                  >
                    {photosArray.map((file, index) => (
                      <ImageThumb
                        key={index}
                        url={file?.url || file}
                        file={file?.url || file}
                        removeAttachment={() =>
                          removeAttachments(
                            index,
                            'transporter_bill_photos',
                            values.transporter_bill_photos,
                            setFieldValue
                          )
                        }
                        style={{
                          width: '100px',
                          height: '100px',
                        }}
                        dataCy={{
                          'data-cy':
                            'mandi.gateIn.transporterBill.removeAttachments',
                        }}
                      />
                    ))}
                  </div>
                </div>
              );
            })()}
          </Grid>
        )}
      </ItemGridContainer>
      <hr />
      <FieldArray
        name='farmer_details'
        render={arrayHelpers => (
          <div style={{ marginLeft: '1rem' }}>
            <Typography variant='subtitle1' display='block'>
              <b>Farmer Details</b>
            </Typography>

            {values.farmer_details.map((detail, index) => (
              <div key={index}>
                <Card style={{ margin: '1rem 0' }}>
                  <CardContent>
                    <FarmerDetails
                      key={index}
                      index={index}
                      gatein_id={gatein_id}
                      products={products}
                      skus={skus}
                      showCrossIcon={
                        values?.farmer_details?.length > 1 && !gatein_id
                      }
                      isGrossRequired={isGrossRequired}
                      removeFarmerDetails={() => arrayHelpers.remove(index)}
                      farmerId={values.farmer_details[index].farmer?.id}
                      packTypes={packTypes}
                      skuSizes={skuSizes}
                      disabled={!!gatein_id}
                    />
                    {gatein_id && (
                      <Button
                        color='primary'
                        size='medium'
                        onClick={() =>
                          onDownloadPriceSlip && onDownloadPriceSlip(detail)
                        }
                        variant='contained'
                        className={[marginTop6]}
                        data-cy='mandi.gateIn.printSlips'
                      >
                        Price Slip
                      </Button>
                    )}
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        )}
      />
      <div style={{ margin: '1rem' }}>
        {(() => {
          const photos = values?.gatein_photos;
          if (!photos) return null;

          // Convert object format {id: url} to array format
          const photosArray = Array.isArray(photos)
            ? photos
            : Object.entries(photos).map(([id, url]) => ({ id, url }));

          if (photosArray.length === 0) return null;

          return (
            <div style={{ marginBottom: '1rem' }}>
              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fill, minmax(120px, 1fr))',
                  gap: '1rem',
                  maxWidth: '600px',
                }}
              >
                {photosArray.map((_file, index) => (
                  <ImageThumb
                    key={index}
                    file={_file?.url || _file}
                    url={_file?.url || _file}
                    style={{ width: '120px', height: '120px' }}
                    removeAttachment={() =>
                      removeAttachments(
                        index,
                        'gatein_photos',
                        values.gatein_photos,
                        setFieldValue
                      )
                    }
                    dataCy={{
                      'data-cy': 'mandi.gateIn.farmerDetails.removeAttachments',
                    }}
                  />
                ))}
              </div>
            </div>
          );
        })()}
        <Box>
          <ImageUploadButton
            title='Upload PH Slip Images'
            variant='contained'
            color='primary'
            size='large'
            startIcon={null}
            images={values?.gatein_photos || []}
            onImagesChange={newImages => {
              setFieldValue('gatein_photos', newImages);
              if (setGateinPhotos) {
                setGateinPhotos(newImages);
              }
            }}
            saveImages={saveImages}
            removeAttachments={removeAttachments}
            setFieldValue={setFieldValue}
            handleUpload={handleUpload}
            upload={upload}
            setPhotos={setGateinPhotos}
            maxImages={10}
            acceptedFormats='image/*'
            webcamConstraints={{ height: '300', width: '300' }}
            webcamStyle={{ height: '300', width: '300' }}
            showMandatoryMessage={!!PHSlipUploadMandatory}
            mandatoryMessage={PHSlipUploadMandatory}
            sx={{
              minWidth: 200,
              py: 1.5,
              fontSize: '1rem',
              fontWeight: 600,
            }}
            data-cy='mandi.gateIn.uploadButton'
          >
            Upload PH Slip
          </ImageUploadButton>
          {PHSlipUploadMandatory && (
            <Typography
              variant='body2'
              color='error'
              sx={{ mt: 1, fontWeight: 500 }}
            >
              {PHSlipUploadMandatory}
            </Typography>
          )}
        </Box>
      </div>
    </div>
  );
};

export default GateInDetails;

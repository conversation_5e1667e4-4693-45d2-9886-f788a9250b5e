import React, { useEffect, useState } from 'react';

import { Cancel as CancelIcon } from '@mui/icons-material';
import { Box, Button, Grid, Typography } from '@mui/material';
import { FieldArray, useFormikContext } from 'formik';
import { debounce } from 'lodash';

import { useSiteValue } from 'App/SiteContext';
import { Modal as CustomModal } from 'Components';
import { FieldCombo, FieldInput } from 'Components/FormFields';
import FarmerRegistrationForm from 'Pages/Registration/Farmer';
import { getPartners, getPendingCrates } from 'Services/purchaseOrder';
import { getPartnerDetails } from 'Services/register';
import { filterOptions } from 'Utilities';
import { INWARD_TYPE } from 'Utilities/constants';
import { validateRequired } from 'Utilities/formvalidation';
// import { ReceiveCratesPopup } from 'vg-library';

import useAtomicStyles from '../../theme/AtomicCss';

import AppleBoxInwardEntry from './AppleBoxInwardEntry';
import CancelTokenModal from './CancelTokenModal.jsx';
import FarmerStatus from './FarmerStatus.jsx';

const FarmerDetails = ({
  index,
  gatein_id = '',
  products = [],
  skuSizes = [],
  packTypes = [],
  skus = [],
  isGrossRequired = false,
  showCrossIcon = false,
  removeFarmerDetails = () => {},
  disabled,
}) => {
  const [farmers, setFarmers] = useState([]);
  const { values, setFieldValue } = useFormikContext();
  const [open, setOpen] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [farmerStatus, setFarmerStatus] = useState({});

  const getUpdatedFarmers = query => {
    getPartners({ q: query }).then(({ items = [] }) => {
      setFarmers(items);
    });
  };

  useEffect(() => {
    if (products.length === 1) {
      setFieldValue('farmer_details[0].items[0].product', products[0]);
    }
  }, [products]);

  const handleFarmerDetails = ({ id }) => {
    getPartnerDetails(id).then(res =>
      setFieldValue(`farmer_details.${index}.farmer`, res)
    );
  };

  const toggleModal = () => {
    setOpen(!open);
  };

  const selectedProducts = values?.farmer_details?.map(
    ({ items = [] }) =>
      items?.map(({ product = {} }) => product?.id || '') || []
  );

  const cancelToggleModal = () => {
    setModalOpen(!modalOpen);
  };

  const handleStatus = values => {
    setFarmerStatus(values);
  };

  const farmerDetails = values.farmer_details[index]?.farmer;

  return (
    <Grid
      container
      style={{ position: 'relative' }}
      sx={{
        display: 'flex',
        alignItems: 'flex-start',
        justifyContent: 'flex-start',
        flexDirection: 'column',
      }}
    >
      <Grid item md={12}>
        <Grid container md={6}>
          <Typography variant='h3' component='span'>
            {values?.farmer_details?.[index]?.token}
          </Typography>
        </Grid>
        {showCrossIcon && (
          <CancelIcon
            fontSize='large'
            onClick={removeFarmerDetails}
            style={{ cursor: 'pointer', position: 'absolute', right: '0' }}
            data-cy={`mandi.gateIn.farmerDetails.cancelFarmerDetails.${index}`}
          />
        )}
      </Grid>
      <Grid item md={12} style={{ justifyContent: 'end' }}>
        <Grid>
          {values?.farmer_details?.[index].is_cancellable &&
            !values?.farmer_details?.[index].is_cancelled && (
              <Button
                color='error'
                size='small'
                variant='contained'
                onClick={cancelToggleModal}
                data-cy={`mandi.gateIn.farmerDetails.cancelToken.${index}`}
              >
                Cancel Token
              </Button>
            )}
        </Grid>
      </Grid>
      <Grid item md={12} />
      <Grid item md={12} spacing={3}>
        <Grid container md={12} direction='row'>
          <Grid item md={12}>
            <div style={{ display: 'flex', gap: '16px' }}>
              <FieldCombo
                name={`farmer_details.${index}.farmer`}
                label='Contact Number/Name'
                placeholder='Search Farmer'
                size='small'
                variant='outlined'
                options={farmers || []}
                onChangeInput={debounce(q => getUpdatedFarmers(q), 300)}
                filterOptions={filterOptions}
                validate={validateRequired}
                optionLabel={({ name = '', phone_number = '' }) =>
                  `${name}-${phone_number}`
                }
                onChange={e => {
                  handleStatus(e);
                }}
                style={{ margin: '12px 0', width: '250px' }}
                InputLabelProps={{
                  required: true,
                  shrink: true,
                }}
                disabled={disabled}
                inputProps={{
                  'data-cy': `mandi.gateIn.farmerDetails.searchFarmer.${index}`,
                }}
              />
              <FieldInput
                name={`farmer_details.${index}.items.0.farmer_marka`}
                label='Marka'
                placeholder='Marka'
                type='text'
                size='small'
                variant='outlined'
                style={{ margin: '12px 0', width: '100px' }}
                InputLabelProps={{
                  shrink: true,
                }}
                disabled={disabled}
              />
              <Box sx={{ mt: 1 }}>
                <Button
                  variant='outlined'
                  color='primary'
                  size='small'
                  onClick={toggleModal}
                  className='float-right'
                  data-cy={`mandi.gateIn.farmerDetails.addVendor.${index}`}
                  sx={{
                    borderColor: 'primary.light',
                    color: 'primary.main',
                    backgroundColor: 'transparent',
                    textTransform: 'none',
                    fontWeight: 500,
                    borderRadius: 2,
                    px: 2,
                    py: 0.5,
                    '&:hover': {
                      borderColor: 'primary.main',
                      backgroundColor: 'primary.light',
                      color: 'primary.contrastText',
                    },
                    transition: 'all 0.2s ease-in-out',
                  }}
                >
                  + Add Vendor
                </Button>
              </Box>
              {farmerStatus?.id && <FarmerStatus farmerStatus={farmerStatus} />}
            </div>
            <CustomModal
              title='Add Vendor'
              open={open}
              onClose={toggleModal}
              dataCy={{ 'data-cy': 'mandi.farmerRegistration.closeModal' }}
            >
              <FarmerRegistrationForm
                toggleModal={toggleModal}
                handleFarmerDetails={handleFarmerDetails}
                isModal
              />
            </CustomModal>
          </Grid>
        </Grid>

        {/* Apple Box Inward Entry Section */}
        <Grid container md={12} direction='row' spacing={3}>
          <Grid item xs={12}>
            <AppleBoxInwardEntry
              index={index}
              products={products}
              skuSizes={skuSizes}
              packTypes={packTypes}
              skus={skus}
              gatein_id={gatein_id}
              disabled={!!gatein_id}
            />
          </Grid>

          <CancelTokenModal
            data={values?.farmer_details?.[index]}
            open={modalOpen}
            toggleModal={cancelToggleModal}
          />
        </Grid>
      </Grid>
    </Grid>
  );
};

export default FarmerDetails;

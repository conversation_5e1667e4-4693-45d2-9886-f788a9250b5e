import React, { useEffect, useRef, useState } from 'react';

import { Button, Grid, Paper, DialogContentText, Box } from '@mui/material';
import { Formik } from 'formik';
import queryString from 'query-string';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useReactToPrint } from 'react-to-print';

import { useSiteValue } from 'App/SiteContext';
import {
  AppButton,
  Carousel,
  ConfirmationDialog,
  Modal as CustomModal,
} from 'Components';
import PageLayout from 'Components/PageLayout';
import PrintTokenSlipWrapper from 'Components/PrintSlips/PrintTokenSlipWrapper';
import useNotify from 'Hooks/useNotify';
import GateInConfirmPopUp from 'Pages/GateIn/GateInConfirmPopUp.jsx';
import {
  getFarmersById,
  getPackagingBoms,
  getTransportersById,
} from 'Services/common';
import {
  createGateIn,
  getGateInById,
  updateGateIn,
  createAuction,
} from 'Services/gateIn';
import { getPartnerDetails } from 'Services/register';
import {
  getSkuList,
  getSkuSizes,
  getProducts,
  getPackTypeDetails,
} from 'Services/regrade';
import { getTokenListing } from 'Services/token';
import { validateMandiMix } from 'Utilities';
import { INWARD_TYPE, PACKAGING_ITEM } from 'Utilities/constants';
import {
  FARMER_DATA_STRUCTURE,
  GATEIN_TYPE,
  MANDI_TYPE,
  STATUS,
} from 'Utilities/constants/lots';
import {
  getDateMonthFormat,
  getHoursInMs,
  getStartOfPreviousDay,
  getStartOfToday,
} from 'Utilities/dateUtils';
import fileUpload from 'Utilities/fileUpload';
import { getFilteredPackagingItems } from 'Utilities/packagingItem';
import {
  transformApiDataToPriceSlipFormat,
  getBrandLogo,
} from 'Utilities/priceSlipUtils';

import useAtomicStyles from '../../theme/AtomicCss';
import TransporterRegistrationForm from '../Registration/Transporter';

import ChangeDateModal from './ChangeDateModal.jsx';
import GateInDetails from './GateInDetails.jsx';

const HOURS_IN_DAY = 24;
const NEXT_DAY_GATEIN_START = 15;

const GateInForm = () => {
  const [confirm, setConfirm] = useState(false);
  const [gateInSuccess, setGateInSuccess] = useState(false);
  const [successData, setSuccessData] = useState({});
  const [gateIn, setGateIn] = useState({
    farmer_details: [FARMER_DATA_STRUCTURE],
    inwards: INWARD_TYPE.CRATES,
    transporter: null,
    transporter_bill_photos: null,
  });
  const [transporters, setTransporters] = useState(null);
  const [products, setProducts] = useState([]);
  const [skus, setSkus] = useState([]);
  const [skuSizes, setSkuSizes] = useState([]);
  const [packTypes, setPackTypes] = useState([]);
  const [isGrossRequired, setIsGrossRequired] = useState(false);
  const [gateinPhotos, setGateinPhotos] = useState([]);
  const [openCarousel, setOpenCarousel] = useState(false);
  const [packagingItem, setPackagingItem] = useState({});
  const [PHSlipUploadMandatory, setPHSlipUploadMandatory] = useState('');
  const [createAuctionModal, setCreateAuctionModal] = useState(false);
  const {
    mandiList,
    mandiId,
    auctionDate,
    setAuctionDate,
    setSatelliteId,
    satelliteId,
    satelliteList,
    mandiConfig: { auction_id = '' },
  } = useSiteValue();
  const [openDateModal, setDateModal] = useState(false);
  const [upload, setUpload] = useState(false);
  const [loading, setLoading] = useState(false);
  const [openTransporterModal, setOpenTransporterModal] = useState(false);
  const { marginTop6 } = useAtomicStyles();
  const [isSatellite, setIsSatellite] = useState(true);
  const notifier = useNotify();
  const { gatein_id = '' } = useParams();
  const navigate = useNavigate();
  const location = useLocation();

  // Price slip state variables
  const [priceSlipData, setPriceSlipData] = useState(null);
  const [showPriceSlipPreview, setShowPriceSlipPreview] = useState(false);
  // const priceSlipRef = useRef(); // No longer needed - handled internally in wrapper

  function findWordInUrl(url, word) {
    const parts = url.split('/');
    return parts.includes(word);
  }

  const URLvalue = findWordInUrl(location.pathname, MANDI_TYPE.SATELLITE);

  useEffect(() => {
    setIsSatellite(URLvalue);
  }, [location.pathname]);

  const getTransporters = q =>
    getTransportersById({ ...q, limit: 25, offset: 0 }).then(
      ({ items = [] }) => {
        setTransporters(items);
      }
    );

  const setCarouselStatus = () => setOpenCarousel(!openCarousel);

  const { sold = false } = queryString.parse(location.search, {
    arrayFormat: 'separator',
    arrayFormatSeparator: '|',
  });

  const toggleConfirm = () => {
    setConfirm(!confirm);
  };

  const toggleTransporterModal = () => {
    setOpenTransporterModal(!openTransporterModal);
  };

  const toggleGateInSuccess = () => {
    setGateInSuccess(!gateInSuccess);
  };

  const toggleCreateAuctionModal = () => {
    setCreateAuctionModal(!createAuctionModal);
  };

  const handleTransporterDetails = ({ id }, setFieldValue) => {
    getPartnerDetails(id).then(res => {
      setFieldValue('transporter', res);
    });
  };

  useEffect(() => {
    if (gateIn.mandi_id && gateIn.mandi_id !== mandiId) {
      navigate('/');
      return notifier(
        `Mandi is different for your gatein, change your mandi to ${gateIn.mandi_name}`,
        'error'
      );
    }
  }, [gateIn]);

  useEffect(() => {
    if (mandiList && mandiId) {
      const isGross =
        mandiList?.find(({ id = '' }) => id === mandiId)
          ?.gross_weight_require || false;
      setIsGrossRequired(isGross);
    }
  }, [mandiList]);

  useEffect(() => {
    if (!gatein_id) {
      let gateInStartTime;
      if (
        new Date().getTime() - getStartOfToday() <
        getHoursInMs(NEXT_DAY_GATEIN_START)
      ) {
        gateInStartTime =
          getStartOfPreviousDay({ numOfDays: 1 }) +
          getHoursInMs(NEXT_DAY_GATEIN_START);
      } else {
        gateInStartTime =
          getStartOfToday() + getHoursInMs(NEXT_DAY_GATEIN_START);
      }

      const gateInEndTime = gateInStartTime + getHoursInMs(HOURS_IN_DAY);

      if (auctionDate < gateInStartTime || auctionDate > gateInEndTime) {
        setDateModal(
          gateInStartTime + getHoursInMs(HOURS_IN_DAY - NEXT_DAY_GATEIN_START)
        );
      }
    }

    // Fetch APIs for mandi data
    Promise.all([
      getProducts(mandiId),
      getSkuList(mandiId),
      getSkuSizes(mandiId),
      getPackTypeDetails({ auction_id }),
    ]).then(
      ([
        { items: products = [] } = {},
        { items: skuList = [] } = {},
        { items: skuSizes = [] } = {},
        { responseData: packTypes = [] } = {},
      ]) => {
        setProducts(products);
        setSkus(skuList);
        setSkuSizes(skuSizes);
        setPackTypes(packTypes);
      }
    );
  }, [auction_id]);

  const getGateInData = ({ gatein_id = '', products = [], skus = [] }) => {
    if (gatein_id && products?.length && skus?.length) {
      getGateInById({ gatein_id })
        .then(({ responseData = {} }) => {
          const {
            farmer_details,
            auction_date = '',
            inward_type,
            gatein_photos,
            satellite_cc_id,
          } = responseData;
          responseData.gatein_photos = gatein_photos || [];

          if (auction_date) {
            setAuctionDate(auction_date);
          }
          if (satellite_cc_id) {
            setSatelliteId(satellite_cc_id);
          }

          const transporter = {
            name: responseData.transporter_name,
            id: responseData.transporter_id,
          };
          if (responseData.transporter_id) setTransporters([transporter]);

          responseData.inwards = inward_type;
          responseData.self = responseData.gatein_type === GATEIN_TYPE.SELF;
          const farmer_ids = [
            ...new Set(farmer_details.map(({ farmer_id = '' }) => farmer_id)),
          ];
          return {
            farmer_ids,
            responseData,
            transporter: responseData.transporter_id ? transporter : null,
          };
        })
        .then(
          ({ responseData = {}, farmer_ids, transporter: transporterData }) => {
            getFarmersById({ ids: farmer_ids.join() }).then(
              ({ items = [] }) => {
                const farmersByIdMap = items.reduce((acc, item) => {
                  acc[item?.id] = item;
                  return acc;
                }, {});

                responseData.farmer_details =
                  responseData.farmer_details.reduce(
                    (acc, { farmer_id = '', items = [], ...rest }) => {
                      const itemsData = items.reduce((acc, item) => {
                        acc.push({
                          ...item,
                          product:
                            products.find(
                              ({ id = '' }) => item?.product_id === id
                            ) || null,
                          sku:
                            skus.find(({ id = '' }) => item?.sku_id === id) ||
                            null,
                        });
                        return acc;
                      }, []);
                      acc.push({
                        ...rest,
                        farmer: farmersByIdMap[farmer_id] || null,
                        status: items?.[0]?.status,
                        items: itemsData,
                      });
                      return acc;
                    },
                    []
                  );
                responseData.transporter = transporterData;
                console.log(responseData);
                setGateIn(responseData);
              }
            );
          }
        );
    }
  };

  useEffect(() => {
    getGateInData({ gatein_id, products, skus });
  }, [gatein_id, products, skus]);

  const submitGateIn = async values => {
    setLoading(true);
    const {
      vehicle_number = '',
      transporter = {},
      driver_phone_number = '',
      transporter_amount = 0,
      inwards = '',
      gatein_photos = [],
      transporter_bill_photos = [],
    } = values;

    let captureData, transporterBill;

    try {
      // Handle gatein photos upload
      captureData = await fileUpload(gateinPhotos, 'gatein');
    } catch (error) {
      notifier('Failed to upload gatein photos. Please try again.', 'error');
      notifier(error.error, 'error');
      setLoading(false);
      return;
    }

    try {
      // Handle transporter bill photos upload
      transporterBill = await fileUpload(transporter_bill_photos, 'gatein');
    } catch (error) {
      notifier(
        'Failed to upload transporter bill photos. Please try again.',
        'error'
      );
      notifier(error.error, 'error');
      setLoading(false);
      return;
    }

    const gateInIds = Array.isArray(gatein_photos)
      ? gatein_photos.map(({ id = '' }) => id).filter(id => id !== '')
      : [];
    const capture = captureData?.length ? [...gateInIds, ...captureData] : [];

    const transporterBillIds = Array.isArray(transporter_bill_photos)
      ? transporter_bill_photos.reduce((acc, { id = '' }) => {
          if (id) acc.push(id);
          return acc;
        }, [])
      : [];
    const transporter_bill = transporterBill?.length
      ? [...transporterBillIds, ...transporterBill]
      : [];

    if (capture.length === 0 && gateInIds.length === 0) {
      notifier('Please Upload PH Slip', 'error');
      setPHSlipUploadMandatory('* Mandatory to Upload PH Slip');
      setLoading(false);
      return;
    }

    const DEFAULT_DATA = {
      mandi_id: mandiId,
      gatein_type: GATEIN_TYPE.FARMER,
      inward_type: inwards,
      ...(URLvalue ? { satellite_cc_id: satelliteId } : {}),
    };
    // Transform payload for Apple Box Inward Entry (CRATES)
    const transformAppleBoxPayload = farmerDetails => {
      return farmerDetails.map(({ farmer, status, id = undefined, items }) => {
        // Get marka from the first item group that has it
        const firstMarka = items.find(item => item.farmer_marka)?.farmer_marka;

        const transformedItems = items.map(
          ({
            product_id,
            farmer_marka,
            mandi_number,
            items: skuItems,
            ...rest
          }) => {
            console.log(
              'Processing SKU items:',
              skuItems,
              'gatein_id:',
              gatein_id
            );
            // Filter SKU items based on create vs update scenario
            const filteredSkuItems = (
              skuItems?.filter(skuItem => {
                const hasId = skuItem.id || skuItem.gatein_sku_item_id; // Check for various id fields
                const unitsValue = skuItem.units?.toString().trim();

                // In UPDATE mode (gatein_id exists): keep items with id even if units is 0
                if (gatein_id && hasId) {
                  return true;
                }
                // In both CREATE and UPDATE: only keep items with non-empty units
                return unitsValue && unitsValue !== '' && unitsValue !== '0';
              }) || []
            ).reduce((acc, skuItem) => {
              const itemId = skuItem.id || skuItem.gatein_sku_item_id;
              acc.push({
                ...(itemId && gatein_id ? { id: itemId } : {}), // Include id if present in update mode
                sku_id: skuItem.sku_id,
                units: parseInt(skuItem.units?.toString().trim() || 0),
              });
              return acc;
            }, []);

            console.log('Filtered SKU items result:', filteredSkuItems);

            return {
              ...rest,
              product_id: product_id || rest.product_id, // Add missing product_id
              farmer_marka: farmer_marka || firstMarka, // Propagate marka from first item
              items: filteredSkuItems,
            };
          }
        );

        return {
          farmer_id: farmer?.id,
          ...(gatein_id ? { id } : {}),
          items: transformedItems,
        };
      });
    };

    console.log('transformAppleBoxPayload', values?.farmer_details || []);
    const farmer_details =
      inwards === INWARD_TYPE.CRATES
        ? transformAppleBoxPayload(values?.farmer_details || [])
        : values?.farmer_details?.map(
            ({ farmer, status, id = undefined, items }) => ({
              farmer_id: farmer?.id,
              ...(gatein_id ? { id } : {}),
              items: items.map(
                ({ product, sku, gross_weight_in_kgs, units, ...rest }) => {
                  const mandiMix = skus?.find(
                    sku =>
                      validateMandiMix(sku?.grade || '') &&
                      product?.id === sku.product_id
                  );
                  return {
                    ...rest,
                    product_id: product?.id,
                    ...(inwards === INWARD_TYPE.TRUCK
                      ? {
                          sku_id: mandiMix?.id,
                          nfi_packaging_bom_id:
                            packagingItem[PACKAGING_ITEM.LOOSE]?.id,
                          status: STATUS?.TO_BE_GRADED,
                        }
                      : {
                          gross_weight_in_kgs:
                            gross_weight_in_kgs === ''
                              ? null
                              : gross_weight_in_kgs,
                          units,
                          sku_id: sku?.id,
                          nfi_packaging_bom_id:
                            packagingItem[PACKAGING_ITEM.LOOSE]?.id,
                          status,
                        }),
                  };
                }
              ),
            })
          );

    const data = {
      ...DEFAULT_DATA,
      transporter_id: transporter?.id || null,
      transporter_amount: (!!transporter?.id && transporter_amount) || null,
      vehicle_number: vehicle_number?.toUpperCase(),
      driver_phone_number,
      farmer_details,
      ...(capture?.length
        ? { gatein_photos: capture }
        : { gatein_photos: gateInIds }),
      ...(gatein_id
        ? {
            gatein_id: Number(gatein_id),
            auction_date: gateIn?.auction_date,
            id: parseInt(gatein_id),
          }
        : { auction_date: auctionDate }),
      ...(transporter?.id
        ? {
            transporter_bill: transporter_bill?.length
              ? transporter_bill
              : transporterBillIds || [],
          }
        : {}),
    };

    const processGateIn = gatein_id ? updateGateIn : createGateIn;
    processGateIn(data)
      .then(({ responseData = {} }) => {
        setSuccessData(responseData);
        toggleGateInSuccess();
      })
      .finally(() => setLoading(false));
  };

  const backHandler = () => {
    navigate('/app/home');
  };

  const handleUpload = () => {
    setUpload(!upload);
  };

  const saveImages = images => {
    setGateinPhotos([...gateinPhotos, images]);
  };

  const removeAttachments = (index, fieldName, attachments, setFieldValue) => {
    let updatedAttachments;

    if (Array.isArray(attachments)) {
      // Handle array format
      updatedAttachments = [...attachments];
      updatedAttachments.splice(index, 1);
    } else if (attachments && typeof attachments === 'object') {
      // Handle object format {id: url}
      const entries = Object.entries(attachments);
      entries.splice(index, 1);
      updatedAttachments = Object.fromEntries(entries);
    } else {
      updatedAttachments = [];
    }

    setFieldValue(fieldName, updatedAttachments);
    fieldName === 'gatein_photos' &&
      setGateinPhotos(
        Array.isArray(updatedAttachments)
          ? [...updatedAttachments]
          : Object.values(updatedAttachments)
      );
  };

  // Handle price slip download
  const handleDownloadPriceSlip = async (tokenData, formValues) => {
    try {
      // Extract token information correctly
      const { token, token_prefix } = tokenData;

      // For edit page, we might have token and token_prefix in different places
      let fullToken = '';
      if (token_prefix && token) {
        fullToken = `${token_prefix}-${token}`;
      } else if (tokenData.token && tokenData.token.includes('-')) {
        // Token might already be formatted
        fullToken = tokenData.token;
      } else {
        // Try to get from farmer data or other sources
        fullToken = token || tokenData.farmer?.token || '';
      }

      if (!fullToken || fullToken.includes('undefined')) {
        notifier('Unable to find valid token information', 'error');
        return;
      }

      // Fetch SKU sizes to create dynamic grade mapping
      let skuSizesData = null;
      try {
        const skuSizesResponse = await getSkuSizes(mandiId);
        skuSizesData = skuSizesResponse?.data || skuSizesResponse; // Try both .data and direct response
      } catch (error) {
        // Will fall back to the old extractGradeType method in transformApiDataToPriceSlipFormat
      }

      // API call without status filter
      const apiParams = {
        token: fullToken,
        auction_date: auctionDate,
        inward_type: formValues?.inwards || 'CRATES',
        mandi_id: mandiId,
        offset: 0,
        limit: 1,
        include_discount_view: true,
      };

      const response = await getTokenListing(apiParams);

      if (!response?.responseData) {
        notifier(
          'No auction data found for this token. Token may not be processed yet.',
          'error'
        );
        return;
      }

      // The API response is an object keyed by farmer_token_id, not an array
      const responseData = response.responseData;
      const farmerTokenIds = Object.keys(responseData);

      if (farmerTokenIds.length === 0) {
        notifier('No token data found in response', 'error');
        return;
      }

      // Take the first (and likely only) farmer token ID
      const farmerTokenId = farmerTokenIds[0];
      const rowData = responseData[farmerTokenId];

      if (!rowData) {
        notifier('Unable to find token information', 'error');
        return;
      }

      // Create API data structure that matches what the utility function expects
      const apiData = {
        responseData: {
          [farmerTokenId]: rowData,
        },
      };

      // Pass the SKU sizes data to the transform function
      const transformedData = transformApiDataToPriceSlipFormat(
        apiData,
        farmerTokenId,
        skuSizesData
      );

      if (!transformedData) {
        notifier('Unable to generate price slip data', 'error');
        return;
      }

      // Get brand logo from mandi list
      const currentMandi = mandiList?.find(m => m.id === mandiId);

      const brandLogo =
        currentMandi?.brand_url ||
        currentMandi?.logo_url ||
        currentMandi?.mandi_logo ||
        currentMandi?.brand_logo ||
        getBrandLogo();

      // Add additional required props including skuSizes
      const priceSlipProps = {
        ...transformedData,
        tokenNumber: fullToken || transformedData.tokenNumber || '',
        brandLogo: brandLogo,
        mandiName: currentMandi?.name || '',
        mobileNumber:
          rowData?.lots_data?.[0]?.phone_number ||
          transformedData.mobileNumber ||
          '',
        auctionDate: auctionDate || new Date().toISOString(),
        transporterName:
          rowData?.transporter_name || transformedData.transporterName || '',
        vehicleNumber:
          rowData?.vehicle_number || transformedData.vehicleNumber || '',
        skuSizes: skuSizesData?.items || [],
        hasTransporterPayment: (rowData?.transportation_cost || 0) > 0,
      };

      setPriceSlipData(priceSlipProps);

      setShowPriceSlipPreview(true);
    } catch (error) {
      notifier('Error generating price slip: ' + error.message, 'error');
    }
  };

  const togglePriceSlipPreview = () => {
    setShowPriceSlipPreview(!showPriceSlipPreview);
    if (!showPriceSlipPreview) {
      setPriceSlipData(null);
    }
  };

  // Print is now handled internally in PrintTokenSlipWrapper

  useEffect(() => {
    if (!location.pathname.includes('edit')) {
      setGateIn({
        farmer_details: [FARMER_DATA_STRUCTURE],
        inwards: INWARD_TYPE.CRATES,
        transporter: null,
        transporter_bill_photos: null,
        vehicle_number: '',
        driver_phone_number: '',
      });
    }
  }, [location.pathname]);

  const createAuctionForADay = async () => {
    await createAuction({
      mandi_id: mandiId,
      auction_date: auctionDate,
    }).then(() => {
      toggleCreateAuctionModal();
      window.location.reload();
    });
  };

  useEffect(() => {
    if (auction_id === 0) {
      toggleCreateAuctionModal();
    }
    if (auction_id > 0 && createAuctionModal) {
      toggleCreateAuctionModal();
    }
  }, [auction_id]);

  return (
    <PageLayout
      title='Gate In'
      showAuctionDate
      disableAuctionDate={!!gatein_id}
    >
      <Formik
        enableReinitialize
        initialValues={{ ...gateIn }}
        onSubmit={submitGateIn}
      >
        {({
          values,
          touched,
          setTouched,
          validateForm,
          handleSubmit,
          setFieldValue,
        }) => (
          <>
            <PageLayout.Body>
              <Paper style={{ padding: '1rem' }}>
                {!values.self && (
                  <GateInDetails
                    gatein_id={gatein_id}
                    transporters={transporters}
                    getTransporters={getTransporters}
                    setOpenTransporterModal={setOpenTransporterModal}
                    removeAttachments={removeAttachments}
                    setFieldValue={setFieldValue}
                    products={products}
                    skus={skus}
                    isGrossRequired={isGrossRequired}
                    saveImages={saveImages}
                    setGateinPhotos={setGateinPhotos}
                    handleUpload={handleUpload}
                    upload={upload}
                    skuSizes={skuSizes}
                    packTypes={packTypes}
                    onDownloadPriceSlip={detail =>
                      handleDownloadPriceSlip(detail, values)
                    }
                    PHSlipUploadMandatory={PHSlipUploadMandatory}
                  />
                )}
              </Paper>
            </PageLayout.Body>
            {!values?.self && (
              <PageLayout.Footer>
                <AppButton
                  variant='contained'
                  color='inherit'
                  className='margin-horizontal'
                  onClick={backHandler}
                  data-cy='mandi.gateIn.formCancel'
                  sx={{
                    mr: 1,
                  }}
                >
                  Cancel
                </AppButton>
                <AppButton
                  variant='contained'
                  color='primary'
                  className='margin-horizontal'
                  loading={loading}
                  disabled={loading}
                  onClick={() => {
                    validateForm().then(errors => {
                      const possibleErrors = Object.keys(errors);
                      if (
                        values?.inwards === INWARD_TYPE.TRUCK &&
                        !possibleErrors.length
                      ) {
                        handleSubmit();
                      } else if (!possibleErrors.length) {
                        toggleConfirm();
                      }
                      if (possibleErrors) {
                        setTouched({ ...touched, ...errors });
                      }
                    });
                  }}
                  data-cy='mandi.gateIn.formSave'
                >
                  Save
                </AppButton>
              </PageLayout.Footer>
            )}
            <ConfirmationDialog
              title='Confirm Save Gate In'
              open={confirm}
              onConfirm={() => {
                handleSubmit();
                toggleConfirm();
              }}
              onCancel={toggleConfirm}
            >
              <DialogContentText>
                Are you sure you want to save this gate in?
              </DialogContentText>
            </ConfirmationDialog>
            <ConfirmationDialog
              title='Start GateIn'
              open={createAuctionModal}
              onConfirm={() => {
                createAuctionForADay();
              }}
              onCancel={toggleCreateAuctionModal}
            >
              <DialogContentText>
                Start GateIn for{' '}
                <b style={{ color: 'red' }}>
                  {getDateMonthFormat(auctionDate)}
                </b>
              </DialogContentText>
            </ConfirmationDialog>
            <ConfirmationDialog
              title={`Gate in ${gatein_id ? 'updated' : 'created'}`}
              open={gateInSuccess}
              onConfirm={backHandler}
            >
              <Grid container style={{ minWidth: '350px', width: '100%' }}>
                {successData?.farmer_details?.map(
                  ({ token, token_prefix, token_suffix, farmer_id }) => {
                    const farmerData = values?.farmer_details?.find(
                      ({ farmer = {} }) => farmer?.id === farmer_id
                    );
                    const printData = {
                      ...farmerData,
                      token,
                      token_prefix,
                      token_suffix,
                    };

                    return (
                      <React.Fragment key={token}>
                        <Box
                          gap={2}
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <Grid item md={12} xs={12}>
                            <b>Token : {token}</b>
                          </Grid>
                          <Button
                            color='primary'
                            size='medium'
                            onClick={() =>
                              handleDownloadPriceSlip(printData, values)
                            }
                            variant='contained'
                            className={[marginTop6]}
                            data-cy='mandi.gateIn.token.printSlips'
                          >
                            Print Slips
                          </Button>
                        </Box>
                      </React.Fragment>
                    );
                  }
                )}
              </Grid>
            </ConfirmationDialog>
            {!!openCarousel && (
              <Carousel
                openModal
                onClose={setCarouselStatus}
                imageData={values.transporter_bill_photos?.map(
                  file => file?.url || file
                )}
              />
            )}
            <CustomModal
              title='Add Transporter'
              open={openTransporterModal}
              onClose={toggleTransporterModal}
              dataCy={{ 'data-cy': 'mandi.transporterRegistration.closeModal' }}
            >
              <TransporterRegistrationForm
                isModal={true}
                toggleModal={toggleTransporterModal}
                handleTransporterDetails={res =>
                  handleTransporterDetails(res, setFieldValue)
                }
              />
            </CustomModal>
          </>
        )}
      </Formik>
      {/* <ChangeDateModal
        title='Warning!'
        newAuctionDate={openDateModal}
        onClose={() => setDateModal(false)}
      /> */}
      <PrintTokenSlipWrapper
        open={showPriceSlipPreview}
        onClose={togglePriceSlipPreview}
        priceSlipData={priceSlipData}
      />
    </PageLayout>
  );
};

export default GateInForm;

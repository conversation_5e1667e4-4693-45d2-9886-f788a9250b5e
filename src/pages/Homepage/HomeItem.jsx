import React from 'react';

import {
  Card,
  Card<PERSON>ction<PERSON>rea,
  CardContent,
  Typography,
  Avatar,
  Box,
} from '@mui/material';
import { OpenInNew } from '@mui/icons-material';
import { Link } from 'react-router-dom';

import { AppIcons } from 'Components';
import envConfig from 'Config/envConfig';

const HomeItem = ({
  title,
  description,
  subRoute,
  url,
  hoverStyle,
  route,
  setShowOptions,
  setMatchIndex,
  index,
  ...rest
}) => {
  const handleCardClick = (e) => {
    if (route.external) {
      e.preventDefault();
      // Construct full URL using MandiOpsUi base URL
      const fullUrl = `${envConfig.MandiOpsUi}${url}`;
      window.open(fullUrl, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <>
      <Card
        {...rest}
        sx={{
          height: 180,
          width: '100%',
          minWidth: { xs: '280px', sm: '240px', md: '220px' },
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          transition: 'all 0.3s ease-in-out',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
          },
          borderRadius: 2,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        }}
      >
        {/* External link redirect icon */}
        {route.external && (
          <Box
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              zIndex: 2,
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              borderRadius: '50%',
              padding: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <OpenInNew
              sx={{
                fontSize: 16,
                color: 'primary.main',
              }}
            />
          </Box>
        )}
        <CardActionArea
          sx={{ height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'stretch' }}
          component={route.external ? 'div' : Link}
          to={route.external ? undefined : url}
          onClick={route.external ? handleCardClick : undefined}
        >
          <CardContent
            sx={{
              padding: '20px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              textAlign: 'center',
              flex: 1,
              justifyContent: 'center',
              gap: 2,
            }}
          >
            {/* Icon */}
            <Avatar
              sx={{
                bgcolor: 'primary.main',
                width: 56,
                height: 56,
              }}
            >
              <AppIcons
                name={route.icon || 'home'}
                sx={{ fontSize: 28, color: 'white' }}
              />
            </Avatar>

            {/* Title */}
            <Typography
              variant='h6'
              sx={{
                fontWeight: 600,
                fontSize: '1rem',
                lineHeight: 1.2,
                textAlign: 'center',
                color: 'inherit',
                textDecoration: 'none',
              }}
            >
              {title}
            </Typography>
          </CardContent>
        </CardActionArea>
      </Card>
    </>
  );
};

export default HomeItem;

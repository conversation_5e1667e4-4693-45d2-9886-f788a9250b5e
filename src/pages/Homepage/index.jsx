import { useState } from 'react';

import { Grid, Typography } from '@mui/material';
import { Link } from 'react-router-dom';

import PageLayout from 'Components/PageLayout';
import HomeItem from 'Pages/Homepage/HomeItem';
import { ROUTES } from 'Utilities/constants/routeList';
import { USER_PERMISSION } from 'Utilities/constants/userPermission';

import { StyledLink, FullWidthCard } from './styles';

const Homepage = () => {
  const [showOptions, setShowOptions] = useState(false);
  const [matchIndex, setMatchIndex] = useState('');

  const dependencyCheck = [showOptions];
  const allDependenciesValid = dependencyCheck.every(dep => dep);

  const allowedTabs = USER_PERMISSION() || [];

  const ALLOWED_TABS_ID = allowedTabs.map(({ id = '' }) => id);

  const ALLOWED_ROUTES = ROUTES.filter(({ id = '' }) =>
    ALLOWED_TABS_ID.includes(id)
  );

  const showDropdown = (route, index) =>
    (allDependenciesValid && route.subRoute && matchIndex === index) ||
    (route.childrenDropdown && showOptions && matchIndex === index);

  return (
    <PageLayout title='Homepage'>
      <PageLayout.Body
        sx={{
          overflowY: 'auto',
          padding: { xs: 2, sm: 3, md: 4 },
          minHeight: 'calc(100vh - 120px)',
        }}
      >
        <Grid
          container
          spacing={{ xs: 2, sm: 3, md: 3 }}
          sx={{
            position: 'relative',
            maxWidth: '1200px',
            margin: '0 auto',
            justifyContent: { xs: 'center', sm: 'flex-start' },
          }}
        >
          {ALLOWED_ROUTES.map((route, index) => (
            <Grid
              key={index}
              item
              xs={12}
              sm={6}
              md={4}
              sx={{
                position: 'relative',
                display: 'flex',
                minHeight: '200px',
              }}
            >
              <HomeItem
                title={route.title}
                url={route.url}
                subRoute={route.subRoute}
                route={route}
                setShowOptions={setShowOptions}
                setMatchIndex={setMatchIndex}
                index={index}
                hoverStyle='styled-link-hover'
                sx={{ width: '100%' }}
              />
              {showDropdown(route, index) && (
                <FullWidthCard>
                  {route.children?.map(child => (
                    <Link
                      to={`${route.url + child.url}`}
                      key={`${child.label}`}
                      component={StyledLink}
                      data-cy={`mandi.home.${route.id}`}
                    >
                      <Typography
                        variant='h6'
                        style={{ padding: '0.55rem 1rem' }}
                      >
                        {child.label}
                      </Typography>
                    </Link>
                  ))}
                </FullWidthCard>
              )}
            </Grid>
          ))}
        </Grid>
      </PageLayout.Body>
    </PageLayout>
  );
};

export default Homepage;

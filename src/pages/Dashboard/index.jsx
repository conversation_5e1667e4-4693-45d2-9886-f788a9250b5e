import React from 'react';

import ConstructionIcon from '@mui/icons-material/Construction';
import { Box, Typography, Paper, Container } from '@mui/material';

import PageLayout from 'Components/PageLayout';

/**
 * Dashboard page component
 * Currently displays an under construction message
 *
 * @returns {JSX.Element} Dashboard component
 */
const Dashboard = () => {
  return (
    <PageLayout>
      <PageLayout.Body>
        <Container maxWidth='lg' sx={{ py: 4 }}>
          <Paper elevation={2} sx={{ p: 5, textAlign: 'center' }}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                my: 6,
              }}
            >
              <ConstructionIcon
                sx={{ fontSize: 60, mb: 2, color: 'warning.main' }}
              />
              <Typography variant='h4' component='h1' gutterBottom>
                This page is under construction
              </Typography>
            </Box>
          </Paper>
        </Container>
      </PageLayout.Body>
    </PageLayout>
  );
};

export default Dashboard;

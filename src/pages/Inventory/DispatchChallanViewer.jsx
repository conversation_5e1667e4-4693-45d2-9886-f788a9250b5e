import React, { useRef } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  IconButton,
  CircularProgress,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import PrintIcon from '@mui/icons-material/Print';
import PrintDispatchChallan from '../../components/PrintSlips/PrintDispatchChallan/PrintDispatchChallan';
import { useReactToPrint } from 'react-to-print';

const DispatchChallanViewer = ({ open, onClose, challanData, isLoading, error }) => {
  const printRef = useRef();

  const handlePrint = useReactToPrint({
    contentRef: printRef,
    onAfterPrint: () => {
      onClose();
    },
  });

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '8px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        backgroundColor: theme => theme.palette.primary.main,
        color: 'white',
        padding: '16px 24px',
      }}>
        <Typography variant="h6" component="div" sx={{ fontWeight: 'bold' }}>
          Dispatch Challan
        </Typography>
        <IconButton 
          edge="end" 
          color="inherit" 
          onClick={onClose} 
          aria-label="close"
          size="small"
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ padding: '24px' }}>
        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography color="error" variant="body1">
              {error}
            </Typography>
          </Box>
        ) : challanData ? (
          <Box ref={printRef} sx={{ 
            width: '100%', 
            bgcolor: 'white',
            '@media print': {
              width: '100%',
              margin: 0,
              padding: 0,
            }
          }}>
            <div ref={printRef}>
            <PrintDispatchChallan 
              consignor={challanData.consignor}
              consignee={challanData.consignee}
              challanNo={challanData.chalan_number}                      
              date={challanData.chalan_date}                             
              totalBoxes={challanData.total_boxes}                       
              productName={challanData.product_name}                     
              fromLocation={challanData.from_location}                   
              toLocation={challanData.to_location}
              vehicleNo={challanData.vehicle_number}                     
              driverName={challanData.driver_name}
              driverNo={challanData.driver_contact}                      
              ownerName={challanData.vehicle_owner_name}                 
              vehicleType={challanData.vehicle_type}
              lotData={challanData.lot_data}                             
              totalCases={challanData.total_cases}
              freightPerBox={challanData.freight_per_box}
              totalFreight={challanData.total_freight}
              advance={challanData.advance}
              totalFreightInWords={challanData.total_freight_in_words}
              brandLogo={challanData.brandLogo}                          
              gradeInfo={challanData.gradeInfo}                          
              mandiName={challanData.mandi_name}                         
              mandiAddress={challanData.mandi_address}                   
            />
          </div>
          </Box>
        ) : (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body1">
              No challan data available
            </Typography>
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ padding: '16px 24px', borderTop: '1px solid rgba(0,0,0,0.1)' }}>
        <Button 
          onClick={onClose} 
          color="inherit"
          variant="outlined"
          sx={{ 
            mr: 1,
            textTransform: 'none',
            fontWeight: 500,
          }}
        >
          Close
        </Button>
        {challanData && (
          <Button 
            onClick={handlePrint} 
            color="primary" 
            variant="contained"
            startIcon={<PrintIcon />}
            sx={{ 
              minWidth: '100px',
              textTransform: 'none',
              fontWeight: 500,
              boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
            }}
          >
            Print Challan
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default DispatchChallanViewer;

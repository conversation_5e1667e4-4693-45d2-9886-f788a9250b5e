import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Divider,
  Chip,
  CircularProgress,
  useTheme,
  IconButton,
  Grid,
  Avatar
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import PersonIcon from '@mui/icons-material/Person';
import PhoneIcon from '@mui/icons-material/Phone';
import DateRangeIcon from '@mui/icons-material/DateRange';
import InventoryIcon from '@mui/icons-material/Inventory';
import CategoryIcon from '@mui/icons-material/Category';
import GradeIcon from '@mui/icons-material/Grade';
import BusinessIcon from '@mui/icons-material/Business';
import NoDataAvailable from '../../components/NoDataAvailable';

const TransactionsModal = ({ 
  open, 
  onClose, 
  inventoryItem, 
  transactions, 
  isLoading, 
  error 
}) => {
  const theme = useTheme();

  // Format date to a readable format
  const formatDate = (dateInput) => {
    if (!dateInput) return 'N/A';
  
    try {
      const date = new Date(Number(dateInput));
      return date.toLocaleDateString('en-IN', {
        day: '2-digit',
        month: 'short',
        year: 'numeric'
      });
    } catch (e) {
      return 'Invalid Date';
    }
  };
  

  // Render transaction in a compact single-line format
  const renderTransactionRow = (transaction, index) => {
    // Format timestamp as a readable date
    const formattedDate = formatDate(transaction.transaction_date_int);
    
    return (
      <TableRow 
        key={transaction.id || index}
        sx={{ 
          '&:nth-of-type(odd)': { backgroundColor: 'rgba(0, 0, 0, 0.02)' },
          '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' },
          transition: 'background-color 0.2s'
        }}
      >        
        <TableCell sx={{ py: 1.5 }}>
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {transaction.product_name || 'N/A'}
          </Typography>
        </TableCell>
        
        <TableCell sx={{ py: 1.5 }}>
          <Typography variant="body2">
            {transaction.sku_grade || 'N/A'}
          </Typography>
        </TableCell>

        <TableCell sx={{ py: 1.5 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <DateRangeIcon sx={{ mr: 1, color: theme.palette.text.secondary, fontSize: '1rem' }} />
            <Typography variant="body2" sx={{ whiteSpace: 'nowrap' }}>
              {formattedDate}
            </Typography>
          </Box>
        </TableCell>
        
        <TableCell sx={{ py: 1.5 }}>
          <Chip
            label={`${transaction.dispatched_units || 0}`}
            size="small"
            sx={{
              backgroundColor: theme.palette.success.light,
              color: theme.palette.success.dark,
              fontWeight: 'medium',
              minWidth: '40px'
            }}
          />
        </TableCell>
        
        <TableCell sx={{ py: 1.5 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <PersonIcon sx={{ mr: 0.5, color: theme.palette.primary.main, fontSize: '0.9rem' }} />
            <Typography variant="body2" noWrap>
              {transaction.driver_name || 'N/A'}
            </Typography>
          </Box>
        </TableCell>
        
        <TableCell sx={{ py: 1.5 }}>
          <Typography variant="body2" noWrap>
            {transaction.driver_contact || 'N/A'}
          </Typography>
        </TableCell>
        
        <TableCell sx={{ py: 1.5 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <LocalShippingIcon sx={{ mr: 0.5, color: theme.palette.primary.main, fontSize: '0.9rem' }} />
            <Typography variant="body2" noWrap>
              {transaction.vehicle_number || 'N/A'}
            </Typography>
          </Box>
        </TableCell>
        
        <TableCell sx={{ py: 1.5 }}>
          <Typography variant="body2" noWrap>
            {transaction.customer_short_code || 'N/A'}
          </Typography>
        </TableCell>
      </TableRow>
    );
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '8px',
          overflow: 'hidden'
        }
      }}
    >
      <DialogTitle sx={{ 
        backgroundColor: theme.palette.primary.main, 
        color: '#fff',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        p: 2
      }}>
        <Box>
          <Typography variant="h6" component="div">
            Transaction History
          </Typography>
          {inventoryItem && (
            <Typography variant="subtitle2" sx={{ opacity: 0.9, mt: 0.5 }}>
              {inventoryItem.mandi_number && `Mandi Number: ${inventoryItem.mandi_number}`}
              {inventoryItem.token && ` | Token: ${inventoryItem.token}`}
            </Typography>
          )}
        </Box>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            color: '#fff',
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      
      <DialogContent sx={{ p: 0, backgroundColor: '#f9f9f9' }}>
        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 8 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography color="error">{error}</Typography>
          </Box>
        ) : transactions && transactions.length > 0 ? (
          <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
            <Table size="small" sx={{ minWidth: 800 }}>
              <TableHead>
                <TableRow sx={{ backgroundColor: theme.palette.primary.light }}>
                <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>Product</TableCell>
                <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>Grade</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>Date</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>Units</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>Driver</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>Contact</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>Vehicle</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>Customer</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {transactions.map((transaction, index) => renderTransactionRow(transaction, index))}
              </TableBody>
            </Table>
          </TableContainer>
        ) : (
          <Box sx={{ py: 4 }}>
            <NoDataAvailable message="No transaction history available for this item" />
          </Box>
        )}
      </DialogContent>
      
      <DialogActions sx={{ p: 2, backgroundColor: '#f9f9f9' }}>
        <Button 
          onClick={onClose} 
          variant="outlined"
          sx={{ textTransform: 'none' }}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TransactionsModal;

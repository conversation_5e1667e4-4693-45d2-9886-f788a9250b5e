import React, { useState, useEffect } from 'react';
import AdapterDateFns from '@mui/lab/AdapterDateFns';
import LocalizationProvider from '@mui/lab/LocalizationProvider';
import DateTimePicker from '@mui/lab/DateTimePicker';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Typography,
  Grid,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  CircularProgress,
  Box,
  Alert,
} from '@mui/material';

/**
 * Modal for dispatching inventory items
 */
const DispatchModal = ({ open, onClose, inventoryItem, onSubmit, isLoading, error }) => {
  const [lots, setLots] = useState([]);
  const [isFormValid, setIsFormValid] = useState(false);
  const [driverName, setDriverName] = useState('');
  const [driverContact, setDriverContact] = useState('');
  const [vehicleNumber, setVehicleNumber] = useState('');
  const [transactionDate, setTransactionDate] = useState(new Date());

  useEffect(() => {
    if (open && inventoryItem && inventoryItem.mandi_number_lots) {
      // Initialize lots with existing data
      const initialLots = inventoryItem.mandi_number_lots.map(lot => ({
        id: lot.id,
        mandi_number_id: lot.mandi_number_id || inventoryItem.mandi_number_id,
        max_box_count: lot.max_box_count || 0,
        loaded_box_count: lot.loaded_box_count || 0,
        sku_grade: lot.sku_grade || 'N/A',
      }));
      setLots(initialLots);
      // Reset driver info
      setDriverName('');
      setDriverContact('');
      setVehicleNumber('');
      setTransactionDate(new Date());
      validateForm(initialLots);
    } else {
      // Reset if modal is closed or no data
      setLots([]);
      setDriverName('');
      setDriverContact('');
      setVehicleNumber('');
      setTransactionDate(new Date());
      setIsFormValid(false);
    }
  }, [open, inventoryItem]);

  const validateForm = (lotsData) => {
    // Check if lots are valid
    const lotsValid = lotsData.every(lot => 
      lot.loaded_box_count >= 0 && 
      lot.loaded_box_count <= lot.max_box_count
    );
    
    // Check if driver info is valid
    const driverInfoValid = 
      driverName.trim() !== '' && 
      driverContact.trim() !== '' && 
      vehicleNumber.trim() !== '' && 
      driverContact.length >= 10 &&
      transactionDate !== null;
    
    const isValid = lotsValid && driverInfoValid;
    setIsFormValid(isValid);
    return isValid;
  };

  const handleUpdateLoadedCount = (index, value) => {
    const updatedLots = [...lots];
    const numValue = parseInt(value, 10);
    updatedLots[index].loaded_box_count = isNaN(numValue) ? 0 : numValue;
    setLots(updatedLots);
    validateForm(updatedLots);
  };
  
  const handleDriverNameChange = (e) => {
    setDriverName(e.target.value);
    validateForm(lots);
  };
  
  const handleDriverContactChange = (e) => {
    setDriverContact(e.target.value);
    validateForm(lots);
  };
  
  const handleVehicleNumberChange = (e) => {
    setVehicleNumber(e.target.value);
    validateForm(lots);
  };
  
  const handleTransactionDateChange = (date) => {
    setTransactionDate(date);
    validateForm(lots);
  };

  const handleSubmit = () => {
    if (validateForm(lots)) {
      // Transform data for API submission
      const submitData = {
        driver_name: driverName.trim(),
        driver_contact: driverContact.trim(),
        vehicle_number: vehicleNumber.trim(),
        transaction_date: transactionDate.toISOString(),
        mandi_number_id: inventoryItem.mandi_number_id,
        lots: lots.map(lot => ({
          lot_id: lot.id,
          dispatched_units: lot.loaded_box_count
        }))
      };
      onSubmit(submitData);
    }
  };

  if (!open) return null;

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        Dispatch Units for Mandi Number: {inventoryItem?.mandi_number}
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
          Driver Information:
        </Typography>
        
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Driver Name"
              value={driverName}
              onChange={handleDriverNameChange}
              required
              error={driverName.trim() === ''}
              helperText={driverName.trim() === '' ? 'Driver name is required' : ''}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Driver Contact"
              value={driverContact}
              onChange={handleDriverContactChange}
              required
              error={driverContact.trim() === '' || driverContact.length < 10}
              helperText={driverContact.trim() === '' ? 'Driver contact is required' : 
                         driverContact.length < 10 ? 'Enter a valid contact number' : ''}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Vehicle Number"
              value={vehicleNumber}
              onChange={handleVehicleNumberChange}
              required
              error={vehicleNumber.trim() === ''}
              helperText={vehicleNumber.trim() === '' ? 'Vehicle number is required' : ''}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Transaction Date"
              type="datetime-local"
              value={transactionDate ? transactionDate.toISOString().substring(0, 16) : ''}
              onChange={(e) => handleTransactionDateChange(new Date(e.target.value))}
              InputLabelProps={{ shrink: true }}
              required
            />
          </Grid>
        </Grid>
        
        <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
          Update dispatch units:
        </Typography>
        
        {lots.length > 0 ? (
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Lot ID</TableCell>
                <TableCell>Grade</TableCell>
                <TableCell>Units to Dispatch / Available</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {lots.map((lot, index) => (
                <TableRow key={lot.id}>
                  <TableCell>{lot.id}</TableCell>
                  <TableCell>{lot.sku_grade}</TableCell>
                  <TableCell>
                    <Grid container alignItems="center" spacing={1}>
                      <Grid item>
                        <TextField
                          type="number"
                          value={lot.loaded_box_count}
                          onChange={(e) => handleUpdateLoadedCount(index, e.target.value)}
                          inputProps={{ 
                            min: 0,
                            max: lot.max_box_count
                          }}
                          error={lot.loaded_box_count > lot.max_box_count || lot.loaded_box_count < 0}
                          size="small"
                        />
                      </Grid>
                      <Grid item>
                        <Typography variant="body2">/ {lot.max_box_count}</Typography>
                      </Grid>
                    </Grid>
                    {(lot.loaded_box_count > lot.max_box_count || lot.loaded_box_count < 0) && (
                      <Typography variant="caption" color="error">
                        {lot.loaded_box_count > lot.max_box_count 
                          ? `Cannot exceed sold units (${lot.max_box_count})` 
                          : 'Value cannot be negative'
                        }
                      </Typography>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <Typography variant="body2" color="text.secondary">
            No lots available for this mandi number.
          </Typography>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit} 
          color="primary" 
          variant="contained"
          disabled={!isFormValid || isLoading}
        >
          {isLoading ? (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <CircularProgress size={20} sx={{ mr: 1 }} />
              Updating...
            </Box>
          ) : 'Submit'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DispatchModal;

import React, { useState } from 'react';
import DispatchModal from './DispatchModal';
import TransactionsModal from './TransactionsModal';
import { dispatchInventory, fetchInventoryTransactions } from '../../services/inventoryService';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Box,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  useTheme
} from '@mui/material';

const getStatusColor = (status) => {
  switch (status) {
    case 'YET_TO_LOAD':
      return { bg: '#fff0e6', color: '#ff6b00' };
    case 'PARTIALLY_LOADED':
      return { bg: '#e9f6ff', color: '#0076d9' };
    case 'FULLY_LOADED':
      return { bg: '#e6f9ee', color: '#00b86b' };
    default:
      return { bg: '#f5f5f5', color: '#666666' };
  }
};

const InventoryTable = ({ data, status, onUpdateUnits }) => {
  const theme = useTheme();
  const [selectedItem, setSelectedItem] = useState(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [updateLoading, setUpdateLoading] = useState(false);
  const [updateError, setUpdateError] = useState(null);
  
  // State for transactions modal
  const [transactionsModalOpen, setTransactionsModalOpen] = useState(false);
  const [selectedRowForTransactions, setSelectedRowForTransactions] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [transactionsLoading, setTransactionsLoading] = useState(false);
  const [transactionsError, setTransactionsError] = useState(null);
  if (!data || data.length === 0) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body1">No data available</Typography>
      </Box>
    );
  }

  // Helper function to get status colors based on item's loading status
  const getItemStatusColors = (item) => {
    // First check the item's loading_status, then fall back to status prop
    const loadingStatus = item.loading_status || item.status || status;
    return getStatusColor(loadingStatus);
  };
  
  // Helper function to format status text
  const formatStatusText = (statusValue) => {
    if (!statusValue) return 'Unknown';
    return statusValue.replace(/_/g, ' ');
  };
  
  // Handler for Dispatch button click
  const handleLoadUnits = (item) => {
    setSelectedItem(item);
    setUpdateError(null);
    setModalOpen(true);
  };
  
  // Handler for submitting dispatch details
  const handleSubmitUnits = async (data) => {
    setUpdateLoading(true);
    setUpdateError(null);
    
    try {
      // Call the dispatch API directly with complete data
      const response = await dispatchInventory(data);
      console.log('Dispatch successful:', response);
      setModalOpen(false);
      setSelectedItem(null);
    } catch (error) {
      setUpdateError('Failed to dispatch inventory. Please try again.');
      console.error('Error dispatching inventory:', error);
    } finally {
      setUpdateLoading(false);
    }
  };
  
  // Handler for closing the modal
  const handleCloseModal = () => {
    setModalOpen(false);
    setSelectedItem(null);
    setUpdateError(null);
  };
  
  // Handler for row click to show transactions
  const handleRowClick = async (row, event) => {
    // Ignore clicks on buttons or action cells
    if (event.target.closest('button') || event.target.closest('[data-action-cell="true"]')) {
      return;
    }
    
    setSelectedRowForTransactions(row);
    setTransactionsModalOpen(true);
    setTransactionsLoading(true);
    setTransactionsError(null);
    
    try {
      const mandiNumberId = row.mandi_number_id || row.id;
      const fetchedTransactions = await fetchInventoryTransactions(mandiNumberId);
      setTransactions(fetchedTransactions);
    } catch (error) {
      console.error('Error fetching transactions:', error);
      setTransactionsError('Failed to load transaction history. Please try again.');
    } finally {
      setTransactionsLoading(false);
    }
  };
  
  // Handler for closing transactions modal
  const handleCloseTransactionsModal = () => {
    setTransactionsModalOpen(false);
    setSelectedRowForTransactions(null);
  };

  return (
    <>
      <DispatchModal 
        open={modalOpen}
        onClose={handleCloseModal}
        inventoryItem={selectedItem}
        onSubmit={handleSubmitUnits}
        isLoading={updateLoading}
        error={updateError}
      />
      
      <TransactionsModal
        open={transactionsModalOpen}
        onClose={handleCloseTransactionsModal}
        inventoryItem={selectedRowForTransactions}
        transactions={transactions}
        isLoading={transactionsLoading}
        error={transactionsError}
      />
      <TableContainer 
        component={Paper} 
        sx={{ 
          boxShadow: '0px 2px 6px rgba(0, 0, 0, 0.05)', 
          borderRadius: '8px',
          overflow: 'hidden'
        }}
      >
      <Table sx={{ minWidth: 650 }} aria-label="inventory table">
        <TableHead>
          <TableRow sx={{ 
            '& th': { 
              fontWeight: 'bold', 
              backgroundColor: theme.palette.primary.main, 
              color: '#fff',
              fontSize: '0.875rem',
              padding: '16px 12px',
              whiteSpace: 'nowrap' 
            } 
          }}>
            <TableCell>Mandi Number</TableCell>
            <TableCell>Token</TableCell>
            <TableCell>Farmer Name</TableCell>
            <TableCell>Customer</TableCell>
            <TableCell>Price</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Grade</TableCell>
            <TableCell>Auction Date</TableCell>
            <TableCell>Action</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((row, index) => (
            <TableRow
              key={row.id}
              onClick={(event) => handleRowClick(row, event)}
              sx={{ 
                '&:nth-of-type(odd)': { backgroundColor: 'rgba(0, 0, 0, 0.02)' },
                '&:hover': { 
                  backgroundColor: 'rgba(0, 0, 0, 0.04)',
                  cursor: 'pointer'
                },
                '& td': { 
                  padding: '12px', 
                  borderBottom: '1px solid rgba(224, 224, 224, 0.5)',
                  fontSize: '0.875rem'
                },
                transition: 'background-color 0.2s'
              }}
            >
              <TableCell 
                component="th" 
                scope="row"
                sx={{ fontWeight: 'medium' }}
              >
                {row.mandi_number || 'N/A'}
              </TableCell>
              <TableCell sx={{ fontFamily: 'monospace', letterSpacing: '0.5px' }}>
                {row.token ? row.token.trim() : 'N/A'}
              </TableCell>
              <TableCell sx={{ fontWeight: 'medium' }}>
                {row.farmer_name || row.farmer_id || 'N/A'}
              </TableCell>
              <TableCell>
                {row.customer_short_code 
                  ? <>
                      <span style={{ fontWeight: 500 }}>{row.customer_short_code}</span>
                      <span style={{ color: 'rgba(0,0,0,0.6)', fontSize: '0.8rem' }}> ({row.customer_id || 'N/A'})</span>
                    </>
                  : row.customer_id || 'N/A'}
              </TableCell>
              <TableCell sx={{ fontWeight: row.price ? 'medium' : 'normal', color: row.price ? theme.palette.success.dark : 'text.secondary' }}>
                {row.price ? `₹${row.price}` : 'N/A'}
              </TableCell>
              <TableCell>
                <Chip
                  label={formatStatusText(row.status || row.loading_status || status)}
                  sx={{
                    backgroundColor: getItemStatusColors(row).bg,
                    color: getItemStatusColors(row).color,
                    fontWeight: 'medium',
                    fontSize: '0.75rem',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
                    padding: '4px 0',
                    height: '24px'
                  }}
                  size="small"
                />
              </TableCell>
              <TableCell sx={{ fontWeight: row.grade ? 'medium' : 'normal' }}>
                {row.grade || <span style={{ color: 'rgba(0,0,0,0.4)' }}>N/A</span>}
              </TableCell>
              <TableCell sx={{ whiteSpace: 'nowrap' }}>
                {row.auction_date ? (
                  <Box sx={{ 
                    fontSize: '0.8rem', 
                    backgroundColor: 'rgba(0,0,0,0.04)', 
                    padding: '4px 8px', 
                    borderRadius: '4px',
                    display: 'inline-block'
                  }}>
                    {typeof row.auction_date === 'number' 
                      ? new Date(row.auction_date).toLocaleDateString('en-IN', { 
                          day: '2-digit', 
                          month: 'short', 
                          year: 'numeric' 
                        }) 
                      : new Date(row.auction_date).toLocaleDateString('en-IN', { 
                          day: '2-digit', 
                          month: 'short', 
                          year: 'numeric' 
                        })}
                  </Box>
                ) : <span style={{ color: 'rgba(0,0,0,0.4)' }}>N/A</span>}
              </TableCell>
              <TableCell data-action-cell="true">
                {(row.status === 'YET_TO_LOAD' || row.status === 'PARTIALLY_LOADED' || 
                 row.loading_status === 'YET_TO_LOAD' || row.loading_status === 'PARTIALLY_LOADED') && (
                  <Button 
                    variant="contained" 
                    color="primary" 
                    size="small"
                    sx={{ 
                      textTransform: 'none', 
                      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                      '&:hover': {
                        boxShadow: '0 4px 8px rgba(0,0,0,0.15)'
                      }
                    }}
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent row click event
                      handleLoadUnits(row);
                    }}
                  >
                    Dispatch
                  </Button>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
    </>
  );
};

export default InventoryTable;

import React, { useState, useEffect } from 'react';
import AdapterDateFns from '@mui/lab/AdapterDateFns';
import LocalizationProvider from '@mui/lab/LocalizationProvider';
import DatePicker from '@mui/lab/DatePicker';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Typography,
  Grid,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  CircularProgress,
  Box,
  Alert,
} from '@mui/material';

/**
 * Modal for dispatching inventory items
 */
const DispatchModal = ({ open, onClose, inventoryItem, onSubmit, isLoading, error }) => {
  const [lots, setLots] = useState([]);
  const [isFormValid, setIsFormValid] = useState(false);
  const [driverName, setDriverName] = useState('');
  const [driverContact, setDriverContact] = useState('');
  const [vehicleNumber, setVehicleNumber] = useState('');
  const [transactionDate, setTransactionDate] = useState(new Date());

  useEffect(() => {
    if (open && inventoryItem && inventoryItem.mandi_number_lots) {
      const initialLots = inventoryItem.mandi_number_lots.map(lot => {
        console.log('Processing lot:', lot);
        
        // Max available units
        const maxUnits = lot.units ? parseInt(lot.units) : 0;
        
        // Already dispatched units (previously loaded)
        let alreadyDispatchedUnits = 0;
        if (lot.loaded_box_count !== undefined && lot.loaded_box_count !== null) {
          alreadyDispatchedUnits = parseInt(lot.loaded_box_count);
        }
        
        return {
          id: lot.id,
          mandi_number_id: lot.mandi_number_id || inventoryItem.mandi_number_id,
          // Available total units
          max_box_count: maxUnits,
          // Previously dispatched units
          already_dispatched: alreadyDispatchedUnits,
          // New units to dispatch (start at 0)
          dispatched_units: 0,
          sku_grade: lot.sku_grade || 'N/A',
        };
      });
      
      console.log('Initialized lots:', initialLots);
      setLots(initialLots);
      
      setDriverName('');
      setDriverContact('');
      setVehicleNumber('');
      setTransactionDate(new Date());
      validateForm(initialLots);
    } else {
      // Reset if modal is closed or no data
      setLots([]);
      setDriverName('');
      setDriverContact('');
      setVehicleNumber('');
      setTransactionDate(new Date());
      setIsFormValid(false);
    }
  }, [open, inventoryItem]);

  const validateForm = (lotsData) => {
    // Check if lots are valid
    const lotsValid = lotsData.every(lot => 
      lot.dispatched_units >= 0 && 
      lot.dispatched_units <= lot.max_box_count
    );
    
    // Check if driver info is valid
    const driverInfoValid = 
      driverName.trim() !== '' && 
      driverContact.trim() !== '' && 
      vehicleNumber.trim() !== '' && 
      driverContact.length >= 10 &&
      transactionDate !== null;
    
    const isValid = lotsValid && driverInfoValid;
    setIsFormValid(isValid);
    return isValid;
  };

  const handleUpdateDispatchedUnits = (index, value) => {
    const updatedLots = [...lots];
    const numValue = parseInt(value, 10);
    updatedLots[index].dispatched_units = isNaN(numValue) ? 0 : numValue;
    setLots(updatedLots);
    validateForm(updatedLots);
  };
  
  const handleDriverNameChange = (e) => {
    setDriverName(e.target.value);
    validateForm(lots);
  };
  
  const handleDriverContactChange = (e) => {
    setDriverContact(e.target.value);
    validateForm(lots);
  };
  
  const handleVehicleNumberChange = (e) => {
    setVehicleNumber(e.target.value);
    validateForm(lots);
  };
  
  const handleTransactionDateChange = (date) => {
    setTransactionDate(date);
    validateForm(lots);
  };

  const handleSubmit = () => {
    if (validateForm(lots)) {
      // Transform data for API submission
      const submitData = {
        driver_name: driverName.trim(),
        driver_contact: driverContact.trim(),
        vehicle_number: vehicleNumber.trim(),
        // Format date as full ISO string as required by backend
        transaction_date: new Date(transactionDate.setHours(0, 0, 0, 0)).toISOString(),
        mandi_number_id: inventoryItem.id,
        // Only include lots with non-zero dispatched_units
        lots: lots
          .filter(lot => lot.dispatched_units > 0)
          .map(lot => ({
            lot_id: lot.id,
            dispatched_units: lot.dispatched_units
          }))
      };
      onSubmit(submitData);
    }
  };

  if (!open) return null;

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        Dispatch Units for Mandi Number: {inventoryItem?.mandi_number}
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
          Driver Information:
        </Typography>
        
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Driver Name"
              value={driverName}
              onChange={handleDriverNameChange}
              required
              error={driverName.trim() === ''}
              helperText={driverName.trim() === '' ? 'Driver name is required' : ''}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Driver Contact"
              value={driverContact}
              onChange={handleDriverContactChange}
              required
              error={driverContact.trim() === '' || driverContact.length < 10}
              helperText={driverContact.trim() === '' ? 'Driver contact is required' : 
                         driverContact.length < 10 ? 'Enter a valid contact number' : ''}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Vehicle Number"
              value={vehicleNumber}
              onChange={handleVehicleNumberChange}
              required
              error={vehicleNumber.trim() === ''}
              helperText={vehicleNumber.trim() === '' ? 'Vehicle number is required' : ''}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Transaction Date"
              type="date"
              value={transactionDate ? transactionDate.toISOString().split('T')[0] : ''}
              onChange={(e) => handleTransactionDateChange(new Date(e.target.value))}
              InputLabelProps={{
                shrink: true,
              }}
              required
            />
          </Grid>
        </Grid>
        
        <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
          Dispatch Units:
        </Typography>
        
        {lots.length > 0 ? (
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Lot ID</TableCell>
                <TableCell>Grade</TableCell>
                <TableCell>Total Sold Units</TableCell>
                <TableCell>Dispatched Units</TableCell>
                <TableCell>Load Units</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {lots.map((lot, index) => (
                <TableRow key={lot.id}>
                  <TableCell>{lot.id}</TableCell>
                  <TableCell>{lot.sku_grade}</TableCell>
                  <TableCell>{lot.max_box_count}</TableCell>
                  <TableCell>{lot.already_dispatched}</TableCell>
                  <TableCell>
                    <Grid container direction="column" spacing={1}>
                      <Grid item>
                        {lot.max_box_count > lot.already_dispatched ? (
                          <TextField
                            type="number"
                            label="Units to Dispatch"
                            value={lot.dispatched_units}
                            onChange={(e) => handleUpdateDispatchedUnits(index, e.target.value)}
                            inputProps={{ 
                              min: 0,
                              max: lot.max_box_count - lot.already_dispatched
                            }}
                            error={lot.dispatched_units > (lot.max_box_count - lot.already_dispatched) || lot.dispatched_units < 0}
                            size="small"
                            variant="outlined"
                            fullWidth
                          />
                        ) : (
                          <Box sx={{ 
                            p: 1, 
                            bgcolor: '#f5f5f5', 
                            border: '1px solid #e0e0e0',
                            borderRadius: 1,
                            color: 'text.disabled',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between'
                          }}>
                            <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
                              All units dispatched
                            </Typography>
                            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                              0
                            </Typography>
                          </Box>
                        )}
                      </Grid>
                      {lot.max_box_count > lot.already_dispatched && lot.dispatched_units > (lot.max_box_count - lot.already_dispatched) && (
                        <Grid item>
                          <Typography variant="caption" color="error">
                            Cannot exceed remaining units ({lot.max_box_count - lot.already_dispatched})
                          </Typography>
                        </Grid>
                      )}
                      {lot.dispatched_units < 0 && (
                        <Grid item>
                          <Typography variant="caption" color="error">
                            Value cannot be negative
                          </Typography>
                        </Grid>
                      )}
                    </Grid>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <Typography variant="body2" color="text.secondary">
            No lots available for this mandi number.
          </Typography>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit} 
          color="primary" 
          variant="contained"
          disabled={!isFormValid || isLoading}
        >
          {isLoading ? (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <CircularProgress size={20} sx={{ mr: 1 }} />
              Updating...
            </Box>
          ) : 'Submit'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DispatchModal;

// IndexedDB helper for managing new customers
const DB_NAME = 'AuctionGradingDB';
const DB_VERSION = 2; // Incremented version to trigger database upgrade
const STORE_NAME = 'newCustomers';

// Initialize IndexedDB
export const initDB = () => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);

    request.onerror = () => {
      reject(new Error('Failed to open IndexedDB'));
    };

    request.onsuccess = event => {
      resolve(event.target.result);
    };

    request.onupgradeneeded = event => {
      const db = event.target.result;

      // Delete old object store if it exists (for upgrade from v1 to v2)
      if (db.objectStoreNames.contains(STORE_NAME)) {
        db.deleteObjectStore(STORE_NAME);
      }

      // Create new object store with db_id as key
      const objectStore = db.createObjectStore(STORE_NAME, {
        keyPath: 'db_id',
        autoIncrement: true,
      });

      // Create indexes for efficient querying
      objectStore.createIndex('mandi_id', 'mandi_id', { unique: false });
      objectStore.createIndex('auction_date', 'auction_date', {
        unique: false,
      });
      objectStore.createIndex('customer_short_code', 'customer_short_code', {
        unique: false,
      });
      objectStore.createIndex('composite_key', ['mandi_id', 'auction_date'], {
        unique: false,
      });
    };
  });
};

// Save new customers to IndexedDB
export const saveNewCustomersToIndexedDB = async (
  customers,
  mandiId,
  auctionDate
) => {
  try {
    const db = await initDB();
    const transaction = db.transaction([STORE_NAME], 'readwrite');
    const store = transaction.objectStore(STORE_NAME);

    // First, clear existing customers for this mandi and auction date
    await clearNewCustomersForSession(mandiId, auctionDate);

    // Add new customers
    const promises = customers.map(customer => {
      return new Promise((resolve, reject) => {
        const customerData = {
          ...customer,
          mandi_id: mandiId,
          auction_date: auctionDate,
          created_at: new Date().toISOString(),
        };
        const request = store.add(customerData);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(new Error('Failed to save customer'));
      });
    });

    await Promise.all(promises);
    return true;
  } catch (error) {
    console.error('Error saving to IndexedDB:', error);
    throw error;
  }
};

// Load new customers from IndexedDB
export const loadNewCustomersFromIndexedDB = async (mandiId, auctionDate) => {
  try {
    const db = await initDB();
    const transaction = db.transaction([STORE_NAME], 'readonly');
    const store = transaction.objectStore(STORE_NAME);
    const index = store.index('composite_key');

    return new Promise((resolve, reject) => {
      const request = index.getAll([mandiId, auctionDate]);

      request.onsuccess = event => {
        const results = event.target.result || [];
        // Filter to ensure we only get exact matches
        const filteredResults = results.filter(
          item => item.mandi_id === mandiId && item.auction_date === auctionDate
        );
        resolve(filteredResults);
      };

      request.onerror = () => {
        reject(new Error('Failed to load customers from IndexedDB'));
      };
    });
  } catch (error) {
    console.error('Error loading from IndexedDB:', error);
    return [];
  }
};

// Clear new customers for a specific session
export const clearNewCustomersForSession = async (mandiId, auctionDate) => {
  try {
    const db = await initDB();
    const transaction = db.transaction([STORE_NAME], 'readwrite');
    const store = transaction.objectStore(STORE_NAME);

    // Get all records for this session
    const customers = await loadNewCustomersFromIndexedDB(mandiId, auctionDate);

    // Delete each record using db_id
    const promises = customers.map(customer => {
      return new Promise((resolve, reject) => {
        const request = store.delete(customer.db_id);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(new Error('Failed to delete customer'));
      });
    });

    await Promise.all(promises);
    return true;
  } catch (error) {
    console.error('Error clearing customers from IndexedDB:', error);
    return false;
  }
};

// Clear old new customers (different auction dates)
export const clearOldNewCustomersFromIndexedDB = async (
  currentMandiId,
  currentAuctionDate
) => {
  try {
    const db = await initDB();
    const transaction = db.transaction([STORE_NAME], 'readwrite');
    const store = transaction.objectStore(STORE_NAME);

    return new Promise((resolve, reject) => {
      const request = store.openCursor();
      const deletePromises = [];

      request.onsuccess = event => {
        const cursor = event.target.result;

        if (cursor) {
          const record = cursor.value;
          // Delete if it's from a different auction date
          if (record.auction_date !== currentAuctionDate) {
            deletePromises.push(
              new Promise((deleteResolve, deleteReject) => {
                const deleteRequest = store.delete(record.db_id);
                deleteRequest.onsuccess = () => deleteResolve();
                deleteRequest.onerror = () =>
                  deleteReject(new Error('Failed to delete old customer'));
              })
            );
          }
          cursor.continue();
        } else {
          // No more records, resolve all delete promises
          Promise.all(deletePromises)
            .then(() => resolve(true))
            .catch(error => reject(error));
        }
      };

      request.onerror = () => {
        reject(new Error('Failed to clear old customers'));
      };
    });
  } catch (error) {
    console.error('Error clearing old customers from IndexedDB:', error);
    return false;
  }
};

// Add a single new customer to IndexedDB
export const addNewCustomerToIndexedDB = async (
  customer,
  mandiId,
  auctionDate
) => {
  try {
    const db = await initDB();
    const transaction = db.transaction([STORE_NAME], 'readwrite');
    const store = transaction.objectStore(STORE_NAME);

    return new Promise((resolve, reject) => {
      const customerData = {
        ...customer,
        id: -4, // Always -4 for new customers
        customer_id: -4, // Always -4 for new customers
        mandi_id: mandiId,
        auction_date: auctionDate,
        created_at: new Date().toISOString(),
      };

      const request = store.add(customerData);

      request.onsuccess = () => {
        resolve(true);
      };

      request.onerror = event => {
        console.error('IndexedDB add error:', event.target.error);
        reject(
          new Error(
            `Failed to add customer: ${event.target.error?.message || 'Unknown error'}`
          )
        );
      };
    });
  } catch (error) {
    console.error('Error adding customer to IndexedDB:', error);
    throw error;
  }
};

// Remove a specific customer from IndexedDB
export const removeCustomerFromIndexedDB = async (
  customerCode,
  mandiId,
  auctionDate
) => {
  try {
    const customers = await loadNewCustomersFromIndexedDB(mandiId, auctionDate);
    const customerToDelete = customers.find(
      c => c.customer_short_code === customerCode
    );

    if (!customerToDelete) {
      return true; // Customer not found, nothing to delete
    }

    const db = await initDB();
    const transaction = db.transaction([STORE_NAME], 'readwrite');
    const store = transaction.objectStore(STORE_NAME);

    return new Promise((resolve, reject) => {
      const request = store.delete(customerToDelete.db_id);

      request.onsuccess = () => {
        resolve(true);
      };

      request.onerror = () => {
        reject(new Error('Failed to remove customer from IndexedDB'));
      };
    });
  } catch (error) {
    console.error('Error removing customer from IndexedDB:', error);
    return false;
  }
};

import { Edit } from '@mui/icons-material';
import {
  <PERSON>,
  Card,
  CardContent,
  Chip,
  Grid,
  IconButton,
  Typography,
} from '@mui/material';
import { Formik } from 'formik';
import PropTypes from 'prop-types';

import { CUSTOMER_NAMES } from 'Utilities/constants';

import SkuComponent from './SkuComponent';

const LotSummaryCard = ({ lot, index, totalLots, onEdit }) => {
  return (
    <Card
      sx={{
        mb: index === totalLots - 1 ? 0 : 2,
        mx: 2,
        boxShadow:
          '0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.24)',
        '&:hover': {
          boxShadow:
            '0px 2px 4px rgba(0, 0, 0, 0.16), 0px 2px 4px rgba(0, 0, 0, 0.28)',
        },
        transition: 'box-shadow 0.2s ease-in-out',
        borderRadius: 2,
      }}
    >
      <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
        {/* Main Information Section */}
        <Box
          sx={{
            display: 'block',
            '@media (min-width: 76rem)': {
              display: 'flex',
              gap: 1,
            },
            mb: 2,
          }}
        >
          {/* Basic Information Section */}
          <Box
            sx={{
              mb: 2,
              '@media (min-width: 76rem)': {
                flex: '0 0 auto',
                mb: 0,
                pt: 1.2,
              },
            }}
          >
            <Grid
              container
              spacing={2}
              pt={1}
              sx={{
                alignItems: 'center',
              }}
            >
              {/* Mandi Number */}
              <Grid
                item
                xs={12}
                sm={4}
                md={4}
                sx={{
                  minWidth: '100px',
                  maxWidth: '200px',
                  flex: 1,
                }}
              >
                <Typography
                  variant='subtitle2'
                  sx={{ fontWeight: 'bold', mb: 0.5 }}
                >
                  Mandi No:
                </Typography>
                <Typography
                  sx={{
                    fontWeight: 'bold',
                    color: '#000000',
                    fontSize: '1rem',
                  }}
                >
                  {lot.mandiNumber?.mandi_number || 'N/A'}
                </Typography>
              </Grid>

              {/* Price */}
              <Grid
                item
                xs={12}
                sm={4}
                md={4}
                sx={{
                  minWidth: '100px',
                  maxWidth: '200px',
                  flex: 1,
                }}
              >
                <Typography
                  variant='subtitle2'
                  sx={{ fontWeight: 'bold', mb: 0.5 }}
                >
                  Price:
                </Typography>
                <Typography
                  sx={{
                    fontWeight: 'bold',
                    color: '#000000',
                    fontSize: '1.1rem',
                  }}
                >
                  ₹{lot.price}
                </Typography>
              </Grid>

              {/* Customer */}
              <Grid
                item
                xs={12}
                sm={3}
                md={3}
                sx={{
                  minWidth: '100px',
                  maxWidth: '200px',
                  flex: 1,
                }}
              >
                <Typography
                  variant='subtitle2'
                  sx={{ fontWeight: 'bold', mb: 0.5 }}
                >
                  Customer:
                </Typography>
                <Typography
                  sx={{
                    fontWeight: 'bold',
                    color: '#000000',
                    fontSize: '1rem',
                  }}
                >
                  {lot.customer_short_code ||
                    CUSTOMER_NAMES[lot.customer_id]?.customer_short_code ||
                    'N/A'}
                </Typography>
              </Grid>

              {/* Edit Button */}
              <Grid
                item
                xs={12}
                sm={1}
                md={1}
                sx={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                  alignItems: 'center',
                }}
              >
                <IconButton
                  onClick={() => onEdit(index)}
                  size='small'
                  sx={{
                    color: 'primary.main',
                    '&:hover': {
                      backgroundColor: 'primary.light',
                      color: 'primary.contrastText',
                    },
                  }}
                >
                  <Edit />
                </IconButton>
              </Grid>
            </Grid>
          </Box>
        </Box>

        {/* SKU, Price, Units Section */}
        {lot.lots && lot.lots.length > 0 && (
          <Box
            sx={{
              mt: 2,
              pt: 2,
              borderTop: '1px solid',
              borderColor: 'divider',
            }}
          >
            <Formik initialValues={{}} onSubmit={() => {}}>
              <SkuComponent lots={lot.lots} isEditable={false} />
            </Formik>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

LotSummaryCard.propTypes = {
  lot: PropTypes.shape({
    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    grade: PropTypes.string,
    price: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    customer_short_code: PropTypes.string,
    customer_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    mandiNumber: PropTypes.shape({
      mandi_number: PropTypes.string,
    }),
    lots: PropTypes.arrayOf(
      PropTypes.shape({
        sku_size_name: PropTypes.string,
        price: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        units: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      })
    ),
  }).isRequired,
  index: PropTypes.number.isRequired,
  totalLots: PropTypes.number.isRequired,
  onEdit: PropTypes.func.isRequired,
};

export default LotSummaryCard;

import React from 'react';

import {
  Box,
  Typography,
  TextField,
  Card,
  Grid,
  Avatar,
  InputAdornment,
} from '@mui/material';
import { useFormikContext } from 'formik';
import PropTypes from 'prop-types';

const SkuComponent = ({ lots, onSkuUpdate, isEditable = true }) => {
  // Always call useFormikContext, but handle when it returns undefined
  const formikContext = useFormikContext();
  const values = formikContext?.values || null;
  const setFieldValue = formikContext?.setFieldValue || null;

  // Handle price input change
  const handlePriceChange = (lot, value) => {
    if (onSkuUpdate) {
      // Find the lot index in the lots array
      const lotIndex = lots.findIndex(l => l.sku_id === lot.sku_id);
      onSkuUpdate(lotIndex, lot.sku_id, 'price', value, setFieldValue);
    }
  };

  if (!lots || lots.length === 0) {
    return (
      <Box p={2}>
        <Typography variant='body2' color='text.secondary'>
          No SKU data available
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        bgcolor: '#F8F9FA',
        borderRadius: 2,
        py: 1,
      }}
    >
      {/* Header Row */}
      <Box sx={{ display: 'flex', gap: 0.5, mb: 1, mr: 0.8 }}>
        {/* SKU Header */}
        <Box
          sx={{
            flex: 1,
            minWidth: '50px',
            maxWidth: '120px',
            textAlign: 'center',
          }}
        >
          <Typography
            variant='caption'
            sx={{
              fontSize: '0.75rem',
              fontWeight: 'bold',
              color: 'primary.main',
              display: 'block',
            }}
          >
            SKU
          </Typography>
        </Box>

        {/* Create header for each lot */}
        {lots.map((lot, index) => (
          <Box
            key={`header-${lot.sku_id}`}
            sx={{ flex: 1, minWidth: '50px', maxWidth: '120px' }}
          >
            <Typography
              variant='caption'
              sx={{
                fontSize: '0.6rem',
                fontWeight: 'bold',
                color: 'text.secondary',
                textAlign: 'center',
                display: 'block',
                lineHeight: 1.2,
                wordBreak: 'break-word',
                minHeight: '2em',
              }}
            >
              {lot.sku_size_name}
            </Typography>
          </Box>
        ))}
      </Box>

      {/* Units Row */}
      <Box sx={{ display: 'flex', gap: 0.5, mb: 1, mr: 0.8 }}>
        {/* Units Label */}
        <Box
          sx={{
            flex: 1,
            minWidth: '50px',
            maxWidth: '120px',
            textAlign: 'center',
          }}
        >
          <Typography
            variant='caption'
            sx={{
              fontSize: '0.75rem',
              fontWeight: 'bold',
              color: 'primary.main',
              display: 'block',
            }}
          >
            Units
          </Typography>
        </Box>

        {/* Units data for each lot */}
        {lots.map((lot, index) => (
          <Box
            key={`units-${lot.sku_id}`}
            sx={{ flex: 1, minWidth: '50px', maxWidth: '120px' }}
          >
            <Typography
              variant='caption'
              sx={{
                fontSize: '0.8rem',
                fontWeight: 'bold',
                color: 'text.primary',
                textAlign: 'center',
                display: 'block',
              }}
            >
              {lot.units}
            </Typography>
          </Box>
        ))}
      </Box>

      {/* Price Row */}
      <Box sx={{ display: 'flex', gap: 0.5, mr: 0.8 }}>
        {/* Price Label */}
        <Box
          sx={{
            flex: 1,
            minWidth: '50px',
            maxWidth: '120px',
            textAlign: 'center',
          }}
        >
          <Typography
            variant='caption'
            sx={{
              fontSize: '0.75rem',
              fontWeight: 'bold',
              color: 'primary.main',
              display: 'block',
            }}
          >
            Price
          </Typography>
        </Box>

        {/* Price inputs for each lot */}
        {lots.map((lot, index) => {
          // Get the current value from Formik or fallback to lot.price
          const fieldPath = `lots.${index}.price`;
          const currentValue = values?.lots?.[index]?.price ?? lot.price ?? '';

          return (
            <Box
              key={`price-${lot.sku_id}`}
              sx={{ flex: 1, minWidth: '50px', maxWidth: '120px' }}
            >
              <TextField
                type='number'
                value={currentValue}
                onChange={e => handlePriceChange(lot, e.target.value)}
                size='small'
                disabled={!isEditable}
                InputProps={{
                  startAdornment: (
                    <InputAdornment
                      position='start'
                      sx={{
                        margin: 0,
                        padding: 0,
                        '& .MuiTypography-root': {
                          fontSize: '0.8rem',
                          margin: 0,
                          padding: 0,
                        },
                      }}
                    >
                      ₹
                    </InputAdornment>
                  ),
                }}
                sx={{
                  width: '100%',
                  '& .MuiInputBase-root': {
                    paddingLeft: '2px',
                  },
                  '& .MuiInputBase-input': {
                    padding: '2px 4px',
                    textAlign: 'center',
                    fontSize: '0.8rem',
                  },
                  '& .MuiInputAdornment-root': {
                    margin: 0,
                    padding: 0,
                    minWidth: 'auto',
                  },
                  '& input[type=number]': {
                    MozAppearance: 'textfield',
                  },
                  '& input[type=number]::-webkit-outer-spin-button': {
                    WebkitAppearance: 'none',
                    margin: 0,
                  },
                  '& input[type=number]::-webkit-inner-spin-button': {
                    WebkitAppearance: 'none',
                    margin: 0,
                  },
                }}
              />
            </Box>
          );
        })}
      </Box>
    </Box>
  );
};

SkuComponent.propTypes = {
  lots: PropTypes.array.isRequired,
  onSkuUpdate: PropTypes.func,
  isEditable: PropTypes.bool,
};

export default SkuComponent;

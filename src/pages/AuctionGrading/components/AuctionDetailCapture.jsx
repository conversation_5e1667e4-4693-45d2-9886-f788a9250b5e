import { useEffect, useState } from 'react';

import { Edit } from '@mui/icons-material';
import AddIcon from '@mui/icons-material/Add';
import CloseIcon from '@mui/icons-material/Close';
import DialpadIcon from '@mui/icons-material/Dialpad';
import {
  Box,
  Button,
  Divider,
  IconButton,
  Typography,
  Chip,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { Formik, useFormikContext } from 'formik';

import { useSiteValue } from 'App/SiteContext';
import GradientDivider from 'Components/GradiantDivider';
import ScreenKeypad from 'Components/ScreenKeypad/index.jsx';
import ScreenNumpad from 'Components/ScreenNumpad/index.jsx';
import useNotify from 'Hooks/useNotify';
import { updateAuctionLot } from 'Services/auctions';
import { CUSTOMER_NAMES, CUSTOMERS } from 'Utilities/constants';

import {
  loadNewCustomersFromIndexedDB,
  saveNewCustomersToIndexedDB,
  addNewCustomerToIndexedDB,
  removeCustomerFromIndexedDB,
  clearOldNewCustomersFromIndexedDB,
} from '../utils/indexedDBHelper';

import Footer from './Footer';
import Header from './Header';
import LotSummaryCard from './LotSummaryCard.jsx';
import SkuComponent from './SkuComponent.jsx';

const CompactClearButton = styled(Button)(() => ({
  minWidth: 0,
  padding: '0 8px', // Increased padding slightly
  height: 32,
  borderRadius: '8.44px',
  backgroundColor: 'rgba(253, 165, 165, 1)',
  fontWeight: 400,
  fontSize: '15px',
  lineHeight: 1.2,
  color: 'rgba(29, 36, 54, 0.6)',
  textTransform: 'none',
  boxShadow: 'none',
  '&:hover': {
    backgroundColor: 'rgba(253, 165, 165, 0.8)',
  },
  // Remove all internal padding from label/content
  '& .MuiButton-startIcon, & .MuiButton-endIcon, & .MuiButton-label, & > span':
    {
      margin: 0,
      padding: 0,
      minWidth: 0,
      lineHeight: 1.2,
    },
}));

const AuctionDetailCapture = ({
  customers,
  lotsInfo,
  token,
  handleCancel,
  skuData,
  mandiNumberInfo,
}) => {
  const notify = useNotify();
  const { mandiId, mandiList, auctionDate } = useSiteValue();
  const [showSummary, setShowSummary] = useState(false);
  const [lotsDetails, setLotDetails] = useState(lotsInfo);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [maxReachedIndex, setMaxReachedIndex] = useState(0);
  const [customerSearch, setCustomerSearch] = useState('');
  const [expandedCustomer, setExpandedCustomer] = useState(false);
  const [filteredCustomers, setFilteredCustomers] = useState([]);
  const [expandedKeypad, setExpandedKeypad] = useState(true);
  const [loading, setLoading] = useState(false);
  const [currentDetail, setCurrentDetail] = useState(lotsDetails[0]);
  const [allCustomers, setAllCustomers] = useState(customers);
  const [sessionNewCustomers, setSessionNewCustomers] = useState([]);

  const [useAlternateUI, setUseAlternateUI] = useState(true);
  const [storedNewCustomers, setStoredNewCustomers] = useState([]);

  // Helper function to load new customers from IndexedDB
  const loadNewCustomersFromStorage = async () => {
    try {
      const customers = await loadNewCustomersFromIndexedDB(
        mandiId,
        auctionDate
      );
      setStoredNewCustomers(customers);

      // Add them to allCustomers if not already present
      setAllCustomers(prev => {
        const existingCodes = prev.map(c =>
          c.customer_short_code.toUpperCase()
        );
        const newOnes = customers.filter(
          c => !existingCodes.includes(c.customer_short_code.toUpperCase())
        );
        return [...prev, ...newOnes];
      });
    } catch (error) {
      console.error('Error loading new customers from IndexedDB:', error);
    }
  };

  // Helper function to save new customers to IndexedDB
  const saveNewCustomersToStorage = async customers => {
    try {
      await saveNewCustomersToIndexedDB(customers, mandiId, auctionDate);
    } catch (error) {
      console.error('Error saving new customers to IndexedDB:', error);
    }
  };

  // Helper function to remove a new customer from storage
  const removeNewCustomerFromStorage = async customerCode => {
    try {
      await removeCustomerFromIndexedDB(customerCode, mandiId, auctionDate);

      const updatedCustomers = storedNewCustomers.filter(
        c => c.customer_short_code !== customerCode
      );
      setStoredNewCustomers(updatedCustomers);

      // Also remove from allCustomers
      setAllCustomers(prev =>
        prev.filter(c => c.customer_short_code !== customerCode)
      );
    } catch (error) {
      console.error('Error removing customer from IndexedDB:', error);
    }
  };

  // Helper function to clear old new customers when auction date changes
  const clearOldNewCustomers = async () => {
    try {
      await clearOldNewCustomersFromIndexedDB(mandiId, auctionDate);
    } catch (error) {
      console.error('Error clearing old new customers from IndexedDB:', error);
    }
  };

  // Helper function to check if a grade has complete input data
  const isGradeComplete = detail =>
    detail.customer_id && detail.price && detail.price.trim() !== '';

  // Function to group customers by character length (2, 3, 4+) and show full label
  const groupCustomersByLength = customers => {
    if (!Array.isArray(customers)) return [];
    const groupedMap = {
      2: [], // 2 characters
      3: [], // 3 characters
      '4+': [], // 4 or more characters
      special: [], // Special group for RTF, AP(JM), and FX
    };

    // Special customer codes that need custom styling
    const specialCustomers = [
      CUSTOMERS.RETURN_TO_FARMER.customer_short_code,
      CUSTOMERS.VEGROW.customer_short_code,
      CUSTOMERS.FRUIT_X.customer_short_code,
    ];

    console.log('groupCustomersByLength', customers);

    customers.forEach(customer => {
      // Use customer_short_code if available, otherwise use customer_temp_short_code
      const shortCode =
        customer?.customer_short_code ||
        customer?.customer_temp_short_code ||
        customer?.name;
      if (!customer || !shortCode) return;

      // Check if this is a special customer (but not new customers with ID -4)
      if (specialCustomers.includes(shortCode)) {
        groupedMap.special.push({
          ...customer,
          customer_short_code: shortCode,
        });
        return; // Skip adding to other groups
      }

      const length = (shortCode.match(/[a-zA-Z0-9]/g) || []).length;
      const group = length === 2 ? '2' : length === 3 ? '3' : '4+';

      groupedMap[group].push({
        ...customer,
        customer_short_code: shortCode,
      });
    });

    // Sort each group alphabetically
    Object.keys(groupedMap).forEach(key => {
      groupedMap[key].sort((a, b) =>
        a.customer_short_code
          .replace(/^[^\w\s]+/, '')
          .localeCompare(b.customer_short_code.replace(/^[^\w\s]+/, ''))
      );
    });

    return [
      { label: '2 Chars', options: groupedMap['2'] },
      { label: '3 Chars', options: groupedMap['3'] },
      { label: '4+ Chars', options: groupedMap['4+'] },
      { label: 'Others', options: groupedMap.special },
    ];
  };

  const handleNext = async () => {
    if (showSummary) {
      setLoading(true);

      // Create mandi_number_details mapping
      const mandiNumberDetails = {};

      console.log('lotsDetails', lotsDetails);
      lotsDetails.flatMap(mandiDetail =>
        mandiDetail.lots?.map(lot => {
          console.log('lot', lot);
          const mandiNumber = lot.mandi_number_id;
          if (mandiNumber) {
            if (!mandiNumberDetails[mandiNumber]) {
              mandiNumberDetails[mandiNumber] = {
                auction_lot_ids: [],
              };
            }
            mandiNumberDetails[mandiNumber]['auction_lot_ids'].push(lot.id);
            mandiNumberDetails[mandiNumber]['total_price'] = +mandiDetail.price;
          }
        })
      );

      const payload = {
        auction_lots: lotsDetails.flatMap(
          mandiDetail =>
            mandiDetail.lots?.map(lot => {
              const lotData = {
                id: lot.id,
                customer_id: mandiDetail.customer_id,
                selling_price:
                  mandiDetail.customer_id === CUSTOMERS.RETURN_TO_FARMER.id ||
                  +lot.price === 0
                    ? 0
                    : (+lot.price + lot.discount_per_unit) /
                      lot.mandi_sku_pack_net_weight_per_unit,
                chute: lot.chute || 0,
                packaging_cost_per_kg: lot.packaging_cost_per_kg,
              };

              // CRITICAL: Add customer_short_code ONLY for new customers (ID = -4)
              if (mandiDetail.customer_id === -4) {
                lotData.customer_short_code = mandiDetail.customer_short_code;
              }

              return lotData;
            }) || []
        ),
        mandi_number_details: mandiNumberDetails,
        source: 'APPLE MANDI AUCTION UI',
      };
      updateAuctionLot(payload)
        .then(() => {
          notify('Submitted Successfully');
          handleCancel();
        })
        .catch(() => {
          notify('Something Went Wrong, Please Try Again Later', 'error');
        })
        .finally(() => {
          setLoading(false);
        });
      return;
    }

    // Skip price validation for RTF customers
    if (
      Number(currentDetail.price) <= 0 &&
      String(currentDetail?.customer_id) !==
        String(CUSTOMERS.RETURN_TO_FARMER.customer_id)
    ) {
      notify('Please Enter Price', 'warning');
      return;
    }
    if (!currentDetail?.customer_id) {
      notify('Please Select Customer', 'warning');
      return;
    }

    setLotDetails(prev => {
      let updatedDetail = { ...prev[selectedIndex], ...currentDetail };

      const updatedLotsDetails = [
        ...prev.slice(0, selectedIndex),
        updatedDetail,
        ...prev.slice(selectedIndex + 1),
      ];

      // Check if we're on the last grade and if all grades are complete after this update
      if (selectedIndex === (lotsInfo || []).length - 1) {
        const allGradesComplete = updatedLotsDetails.every(isGradeComplete);
        if (allGradesComplete) {
          // Automatically go to summary when all grades are complete
          setShowSummary(true);
        }
      }

      return updatedLotsDetails;
    });

    setSelectedIndex(prev => {
      if (prev === (lotsInfo || []).length - 1) {
        // At the last grade, check if all grades are complete
        // We need to use a setTimeout to ensure the state update above has been processed
        setTimeout(() => {
          setLotDetails(currentLotsDetails => {
            const allGradesComplete = currentLotsDetails.every(isGradeComplete);

            if (!allGradesComplete) {
              // Find the first incomplete grade and navigate to it
              const incompleteIndex = currentLotsDetails.findIndex(
                detail => !isGradeComplete(detail)
              );
              if (incompleteIndex !== -1 && incompleteIndex !== prev) {
                notify(
                  'Please complete all grades (customer and price) before viewing summary',
                  'warning'
                );
                setSelectedIndex(incompleteIndex);
                setCurrentDetail(currentLotsDetails[incompleteIndex]);
                setUseAlternateUI(true);
              }
            }
            return currentLotsDetails; // Return unchanged state
          });
        }, 0);

        return prev; // Stay on current grade initially
      }
      console.log('previous currentDetail', currentDetail);
      const newIndex = prev + 1;
      setMaxReachedIndex(prevMax => Math.max(prevMax, newIndex));
      setCurrentDetail(lotsDetails[newIndex]);
      console.log('new currentDetail', currentDetail);
      // Reset to Code tab for each new grade
      setUseAlternateUI(true);
      return newIndex;
    });
  };

  const handleScreenNumPadSubmit = () => {};

  // Helper function to calculate SKU lot prices based on percentage
  const calculateSkuLotPrices = (basePrice, lots) => {
    if (!basePrice || !lots) return lots;

    const updatedLots = lots.map(lot => {
      if (lot.percentage) {
        const calculatedPrice =
          (parseFloat(basePrice) * parseFloat(lot.percentage)) / 100;
        return {
          ...lot,
          price: String(calculatedPrice.toFixed(2)),
          total_price: String(basePrice),
        };
      }
      return lot;
    });
    console.log('updatedLots', updatedLots);
    return updatedLots;
  };

  // Helper function to update Formik values for lots
  const updateFormikLotValues = (lots, setFieldValueFn) => {
    if (typeof setFieldValueFn === 'function') {
      lots.forEach((lot, index) => {
        setFieldValueFn(`lots.${index}.price`, lot.price);
      });
    }
  };

  // Handle individual SKU updates - updates both lotsDetails and Formik values
  const handleSkuUpdate = (lotIndex, skuId, field, value, setFieldValueFn) => {
    // Update lotsDetails
    setLotDetails(prevLotsDetails => {
      const updatedLotsDetails = [...prevLotsDetails];
      if (
        updatedLotsDetails[selectedIndex] &&
        updatedLotsDetails[selectedIndex].lots
      ) {
        const lotToUpdate = updatedLotsDetails[selectedIndex].lots.find(
          lot => lot.sku_id === skuId
        );
        if (lotToUpdate) {
          lotToUpdate[field] = value;
          if (field === 'price') {
            lotToUpdate.manuallyEdited = true;
          }
        }
      }
      return updatedLotsDetails;
    });

    // Update currentDetail
    setCurrentDetail(prevCurrentDetail => {
      const updatedCurrentDetail = { ...prevCurrentDetail };
      if (updatedCurrentDetail.lots) {
        const lotToUpdate = updatedCurrentDetail.lots.find(
          lot => lot.sku_id === skuId
        );
        if (lotToUpdate) {
          lotToUpdate[field] = value;
          if (field === 'price') {
            lotToUpdate.manuallyEdited = true;
          }
        }
      }
      return updatedCurrentDetail;
    });

    // Update Formik values
    if (typeof setFieldValueFn === 'function') {
      setFieldValueFn(`lots.${lotIndex}.${field}`, value);
    }
  };

  const handleScreenNumpadClick = (val, setFieldValueFn) => {
    const newPrice = String(Number(currentDetail.price + val));

    // Update currentDetail
    setCurrentDetail(prev => {
      const updatedData = {
        ...prev,
        price: newPrice,
      };

      // Calculate SKU lot prices based on the new base price
      if (prev.lots && prev.lots.length > 0) {
        const updatedLots = calculateSkuLotPrices(newPrice, prev.lots);
        updatedData.lots = updatedLots;
      }

      return updatedData;
    });

    // Update lotsDetails
    setLotDetails(prevLotsDetails => {
      const updatedLotsDetails = [...prevLotsDetails];
      if (updatedLotsDetails[selectedIndex]) {
        updatedLotsDetails[selectedIndex] = {
          ...updatedLotsDetails[selectedIndex],
          price: newPrice,
        };

        // Calculate SKU lot prices for lotsDetails
        if (
          updatedLotsDetails[selectedIndex].lots &&
          updatedLotsDetails[selectedIndex].lots.length > 0
        ) {
          const updatedLots = calculateSkuLotPrices(
            newPrice,
            updatedLotsDetails[selectedIndex].lots
          );
          updatedLotsDetails[selectedIndex].lots = updatedLots;

          // Update Formik values for all lots
          if (typeof setFieldValueFn === 'function') {
            updateFormikLotValues(updatedLots, setFieldValueFn);
          }
        }
      }
      return updatedLotsDetails;
    });
  };

  const handleScreenNumpadClear = setFieldValueFn => {
    // Update currentDetail
    setCurrentDetail(prev => {
      const updatedData = {
        ...prev,
        price: '',
      };

      // Clear all lot prices
      if (prev.lots && prev.lots.length > 0) {
        const clearedLots = prev.lots.map(lot => {
          return {
            ...lot,
            price: '',
            total_price: '',
          };
        });
        updatedData.lots = clearedLots;
      }
      console.log('clearedLots', updatedData);

      return updatedData;
    });

    // Update lotsDetails
    setLotDetails(prevLotsDetails => {
      const updatedLotsDetails = [...prevLotsDetails];
      if (updatedLotsDetails[selectedIndex]) {
        updatedLotsDetails[selectedIndex] = {
          ...updatedLotsDetails[selectedIndex],
          price: '',
        };

        // Clear all lot prices in lotsDetails
        if (
          updatedLotsDetails[selectedIndex].lots &&
          updatedLotsDetails[selectedIndex].lots.length > 0
        ) {
          const clearedLots = updatedLotsDetails[selectedIndex].lots.map(
            lot => {
              return {
                ...lot,
                price: '',
                total_price: '',
              };
            }
          );
          updatedLotsDetails[selectedIndex].lots = clearedLots;

          // Clear Formik values for all lots
          if (typeof setFieldValueFn === 'function') {
            clearedLots.forEach((lot, index) => {
              setFieldValueFn(`lots.${index}.price`, '');
            });
          }
        }
      }
      return updatedLotsDetails;
    });
  };

  const handleScreenKeypadClick = val => {
    const newSearch = customerSearch + val;
    setCustomerSearch(newSearch);
    filterCustomers(newSearch);
  };

  const filterCustomers = searchTerm => {
    if (!searchTerm) {
      setFilteredCustomers([]);
      return;
    }
    const filtered = allCustomers.filter(customer => {
      const text = (
        customer.customer_short_code || customer.name
      ).toLowerCase();
      const cleanText = text.replace(/^[^\w\s]+/, '');
      const cleanSearchTerm = searchTerm.replace(/^[^\w\s]+/, '');
      return cleanText.startsWith(cleanSearchTerm.toLowerCase());
    });

    setFilteredCustomers(filtered);
  };

  // Function to create a new customer
  const handleCreateNewCustomer = async code => {
    const formattedCode = `(${code.toUpperCase()})`;

    // Check if already exists (case-insensitive)
    const exists = allCustomers.some(
      c =>
        c.customer_short_code.replace(/[()]/g, '').toUpperCase() ===
        code.toUpperCase()
    );

    if (exists) {
      notify('Customer already exists', 'warning');
      return;
    }

    const newCustomer = {
      id: -4, // Always -4 for new customers
      customer_id: -4, // Always -4 for new customers
      customer_short_code: formattedCode,
      name: `New Customer ${formattedCode}`,
      disabled: false,
      isNew: true, // For visual indication
      created_date: new Date().toISOString(),
      mandi_id: mandiId,
    };

    try {
      // Add to IndexedDB
      await addNewCustomerToIndexedDB(newCustomer, mandiId, auctionDate);

      // Add to session state
      setSessionNewCustomers(prev => [...prev, newCustomer]);

      // Add to stored new customers
      setStoredNewCustomers(prev => [...prev, newCustomer]);

      // Add to all customers
      setAllCustomers(prev => [...prev, newCustomer]);

      // Select the new customer - use customer_short_code for selection
      handleCustomer(-4, formattedCode);

      notify(
        `New customer '${formattedCode}' created and saved to database`,
        'success'
      );
    } catch (error) {
      console.error('Error creating new customer:', error);
      notify('Failed to save new customer', 'error');
    }
  };

  const handleScreenKeypadClear = () => {
    setCustomerSearch('');
    setCurrentDetail(prev => {
      const updatedData = {
        ...prev,
        customer_id: 0,
        customer_short_code: '',
      };

      if (prev.lots && prev.lots.length > 0) {
        // When RTF is selected, set all lot prices to '0' to match main price
        const zeroLots = prev.lots.map(lot => {
          return {
            ...lot,
            customer_id: 0,
            customer_short_code: '',
          };
        });
        updatedData.lots = zeroLots;
      }

      return updatedData;
    });

    // Also update lotsDetails to maintain consistency
    setLotDetails(prevLotsDetails => {
      const updatedLotsDetails = [...prevLotsDetails];
      if (updatedLotsDetails[selectedIndex]) {
        updatedLotsDetails[selectedIndex] = {
          ...updatedLotsDetails[selectedIndex],
          customer_id: 0,
          customer_short_code: '',
        };
      }

      // Clear all customer ids in lotsDetails
      if (
        updatedLotsDetails[selectedIndex].lots &&
        updatedLotsDetails[selectedIndex].lots.length > 0
      ) {
        const clearedLots = updatedLotsDetails[selectedIndex].lots.map(lot => {
          return {
            ...lot,
            customer_id: 0,
            customer_short_code: '',
          };
        });
        updatedLotsDetails[selectedIndex].lots = clearedLots;
      }
      return updatedLotsDetails;
    });
  };

  const handleCustomer = (value, label) => {
    // For new customers (ID = -4), check if we're selecting by matching customer_short_code
    // This prevents confusion when multiple new customers exist
    if (value === -4 && label) {
      // Check if the selected customer already exists in currentDetail with same code
      const isAlreadySelected =
        currentDetail?.customer_id === -4 &&
        currentDetail?.customer_short_code === label;

      if (isAlreadySelected) {
        // Already selected, just clear search
        if (customerSearch) {
          setCustomerSearch('');
        }
        return;
      }
    }

    // Update the current detail with selected customer
    setCurrentDetail(prev => {
      // Check if the selected customer is RTF
      const isRTF = String(value) === String(CUSTOMERS.RETURN_TO_FARMER.id);

      const updatedData = {
        ...prev,
        // Set price to '0' if RTF is selected
        price: isRTF ? '0' : prev.price,
        customer_id: value,
        customer_short_code: label || '',
      };

      if (isRTF && prev.lots && prev.lots.length > 0) {
        // When RTF is selected, set all lot prices to '0' to match main price
        const zeroLots = prev.lots.map(lot => {
          return {
            ...lot,
            price: '0',
            total_price: '0',
          };
        });
        updatedData.lots = zeroLots;
      }
      console.log('clearedLots', updatedData);

      return updatedData;
    });

    // Also update lotsDetails to maintain consistency
    setLotDetails(prevLotsDetails => {
      const updatedLotsDetails = [...prevLotsDetails];
      if (updatedLotsDetails[selectedIndex]) {
        const isRTF = String(value) === String(CUSTOMERS.RETURN_TO_FARMER.id);

        updatedLotsDetails[selectedIndex] = {
          ...updatedLotsDetails[selectedIndex],
          price: isRTF ? '0' : updatedLotsDetails[selectedIndex].price,
          customer_id: value,
          customer_short_code: label || '',
        };

        // Update lot prices for RTF case
        if (
          isRTF &&
          updatedLotsDetails[selectedIndex].lots &&
          updatedLotsDetails[selectedIndex].lots.length > 0
        ) {
          const zeroLots = updatedLotsDetails[selectedIndex].lots.map(lot => ({
            ...lot,
            price: '0',
            total_price: '0',
            manuallyEdited: false,
          }));
          updatedLotsDetails[selectedIndex].lots = zeroLots;
        }
      }
      return updatedLotsDetails;
    });

    // Reset customer search if it was being used
    if (customerSearch) {
      setCustomerSearch('');
    }
  };

  const toggleCustomerSearchAndKeypad = () => {
    setExpandedCustomer(prev => !prev);
    setExpandedKeypad(prev => !prev);
  };

  const toggleCustomerSelectionVisibility = () => {
    setUseAlternateUI(prev => !prev);
  };

  const handleEdit = index => {
    setShowSummary(false);
    setSelectedIndex(index);
    setCurrentDetail(lotsDetails[index]);
    // Reset to Code tab when editing from summary
    setUseAlternateUI(true);
  };

  const handleBack = async () => {
    if (showSummary) {
      setShowSummary(false);
      return;
    }

    setSelectedIndex(prev => {
      if (prev !== 0) {
        setCurrentDetail(lotsDetails[prev - 1]);
        // Reset to Code tab when navigating back
        setUseAlternateUI(true);
        return prev - 1;
      }
      return prev;
    });
  };

  const handleGradeClick = gradeIndex => {
    // Allow navigation to any grade (forward or backward)
    if (gradeIndex >= 0 && gradeIndex < lotsDetails.length) {
      setSelectedIndex(gradeIndex);
      setCurrentDetail(lotsDetails[gradeIndex]);

      setShowSummary(false);
      // Reset to Code tab when clicking on grade
      setUseAlternateUI(true);
      // Update maxReachedIndex if navigating to a higher grade
      if (gradeIndex > maxReachedIndex) {
        setMaxReachedIndex(gradeIndex);
      }
    }
  };

  const handleSummaryClick = () => {
    const allGradesComplete = lotsDetails.some(isGradeComplete);
    if (allGradesComplete) {
      setShowSummary(true);
    }
  };

  // Check if all grades are complete for summary tab activation
  const isAllGradesComplete = lotsDetails.some(isGradeComplete);

  useEffect(() => {
    filterCustomers(customerSearch);
  }, [customerSearch]);

  useEffect(() => {
    // For new customers (ID = -4), check for customer_temp_short_code in lots
    if (currentDetail?.customer_id === -4) {
      // Check if customer_short_code is empty string, null, or undefined
      const hasNoShortCode =
        !currentDetail?.customer_short_code ||
        currentDetail.customer_short_code === '';

      console.log('currentDetail', currentDetail);
      if (hasNoShortCode && currentDetail?.lots?.[0]) {
        const firstLot = currentDetail.lots[0];
        console.log('firstLot', firstLot);
        setCustomerSearch(
          firstLot.customer_temp_short_code ||
            firstLot.customer_short_code ||
            ''
        );
      } else {
        setCustomerSearch(currentDetail?.customer_short_code || '');
      }
    } else {
      setCustomerSearch(
        currentDetail?.customer_short_code ||
          CUSTOMER_NAMES[currentDetail?.customer_id]?.customer_short_code ||
          ''
      );
    }
  }, [currentDetail]);

  // Initialize maxReachedIndex based on existing data when component loads
  useEffect(() => {
    let highestFilledIndex = -1;
    lotsDetails?.forEach((lot, index) => {
      if (lot.customer_id && (lot.price || lot.price === '0')) {
        highestFilledIndex = Math.max(highestFilledIndex, index);
      }
    });
    // Allow navigation to the next grade after the last filled one
    setMaxReachedIndex(
      Math.min(highestFilledIndex + 1, lotsDetails?.length - 1)
    );
  }, [lotsDetails]);

  // Load new customers from localStorage on component mount
  useEffect(() => {
    loadNewCustomersFromStorage();
    clearOldNewCustomers();
  }, [mandiId, auctionDate]);

  // Clean up old localStorage entries when auction date changes
  useEffect(() => {
    clearOldNewCustomers();
  }, [auctionDate]);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        // Set height based on viewport minus an offset for the main app header
        // This creates a fixed container for the flex layout to work within.
        height: 'calc(100vh - 70px)',
        minHeight: 'calc(100vh - 70px)', // Ensure minimum height for iPad
        top: 70,
        position: 'fixed',
        overflow: 'hidden', // Prevent this root container from scrolling
        bgcolor: 'white',
        width: '100vw',
        // iPad specific fixes
        '@media (max-width: 1024px) and (orientation: landscape)': {
          height: 'calc(100vh - 70px)',
          minHeight: '600px', // Minimum height for iPad landscape
        },
        '@media (max-width: 768px) and (orientation: portrait)': {
          height: 'calc(100vh - 70px)',
          minHeight: '800px', // Minimum height for iPad portrait
        },
        // Ensure proper viewport handling on iOS Safari
        '@supports (-webkit-touch-callout: none)': {
          height: 'calc(100vh - 70px)',
          minHeight: 'calc(100vh - 70px)',
        },
      }}
    >
      <Header
        token={`C-${token}`}
        grade={lotsDetails?.[selectedIndex]?.mandiNumber.grade}
        farmerMarka={lotsDetails?.[selectedIndex]?.mandiNumber.farmer_marka}
        units={lotsDetails?.[selectedIndex]?.mandiNumber.units}
        totalGrades={lotsInfo?.length}
        currentGrade={selectedIndex + 1}
        isSummary={showSummary}
        gradeNames={lotsDetails?.map(lot => lot.mandiNumber.mandi_number)}
        onGradeClick={handleGradeClick}
        onSummaryClick={handleSummaryClick}
        maxReachedIndex={maxReachedIndex}
        lotsDetails={lotsDetails}
        isAllGradesComplete={isAllGradesComplete}
        skuData={skuData}
        packType={lotsDetails?.[selectedIndex]?.mandiNumber.packType}
        gateinProductName={
          lotsDetails?.[selectedIndex]?.mandiNumber.gatein_product_name
        }
        vehicleNumber={lotsDetails?.[selectedIndex]?.vehicle_number}
      />
      {/* Formik wrapper for entire content area */}
      {!showSummary && (
        <Formik
          initialValues={{
            lots:
              lotsDetails?.[selectedIndex]?.lots?.reduce((acc, lot, index) => {
                acc[index] = { price: lot.price || '' };
                return acc;
              }, {}) || {},
          }}
          enableReinitialize={true}
          onSubmit={() => {}}
        >
          {({ values, setFieldValue }) => {
            return (
              <>
                {/* SKU Component */}
                <SkuComponent
                  lots={lotsDetails?.[selectedIndex]?.lots}
                  onSkuUpdate={(lotIndex, skuId, field, value) =>
                    handleSkuUpdate(
                      lotIndex,
                      skuId,
                      field,
                      value,
                      setFieldValue
                    )
                  }
                  isEditable={
                    // Disable individual SKU editing when customer is RTF
                    String(currentDetail?.customer_id) !==
                    String(CUSTOMERS.RETURN_TO_FARMER.id)
                  }
                />
              </>
            );
          }}
        </Formik>
      )}
      {showSummary && (
        <Box
          flex={1}
          sx={{
            overflow: 'auto',
            py: 2,
            // Add padding bottom for iPad to prevent content being hidden behind fixed footer
            '@media (max-width: 1024px)': {
              paddingBottom: '100px', // Space for fixed footer
            },
          }}
        >
          {lotsDetails.map((lot, index) => (
            <LotSummaryCard
              key={lot.id}
              lot={lot}
              index={index}
              totalLots={lotsDetails.length}
              onEdit={handleEdit}
            />
          ))}
        </Box>
      )}
      {!showSummary && (
        <Box
          display='flex'
          flex={1}
          sx={{
<<<<<<< HEAD
            overflow: 'auto', 
=======
            overflow: 'auto',
>>>>>>> staging
            '@media (max-width: 1024px)': {
              paddingBottom: '100px', // Space for fixed footer
            },
          }}
        >
          {/* PRICE SECTION - Left Side */}
          <Box
            width='50%'
            p={2}
            borderRight={1}
            borderColor='divider'
            sx={{
              display: 'flex',
              flexDirection: 'column',
              minHeight: 'fit-content',
              // iPad specific adjustments
              '@media (max-width: 1024px)': {
                width: '45%', // Slightly smaller to give more space for customer selection
              },
            }}
          >
            {/* Price Header */}
            <Typography variant='h6' fontWeight='bold' mb={2}>
              Enter Price
            </Typography>

            {/* Price Display Box */}
            <Box
              display='flex'
              alignItems='center'
              justifyContent='space-between'
              sx={{
                width: '100%',
                height: 80,
                bgcolor: '#f8f9fa',
                borderRadius: 2,
                px: 3,
                mb: 2,
                border: '1px solid #e0e0e0',
              }}
            >
              <Typography
                variant='h4'
                fontWeight='bold'
                display='flex'
                alignItems='center'
              >
                <Typography
                  component='span'
                  variant='h5'
                  fontWeight='bold'
                  sx={{ color: 'gray', mr: 1 }}
                >
                  ₹
                </Typography>
                {currentDetail?.price ? `${currentDetail?.price}` : ''}
              </Typography>
              {currentDetail?.price && (
                <CompactClearButton
                  onClick={handleScreenNumpadClear}
                  sx={{ ml: 2 }}
                >
                  Clear
                </CompactClearButton>
              )}
            </Box>
            <ScreenNumpad
              onClear={handleScreenNumpadClear}
              onSubmit={handleScreenNumPadSubmit}
              onChange={handleScreenNumpadClick}
              showClear={false}
              showSubmit={false}
              disabled={
                // Convert both to strings for reliable comparison
                String(currentDetail?.customer_id) ===
                String(CUSTOMERS.RETURN_TO_FARMER.id)
              }
            />
          </Box>
          <Box
            flex={1}
            minHeight='fit-content' // Allow content to determine height
            p={2}
            sx={{
              // iPad specific adjustments for customer selection
              '@media (max-width: 1024px)': {
                width: '55%', // More space for customer selection on iPad
                padding: 1, // Reduce padding on smaller screens
              },
            }}
          >
            <Box
              display='flex'
              alignItems='center'
              justifyContent='space-between'
              mb={2}
              sx={{
                width: '100%',
                flexDirection: 'row',
                // Responsive adjustments for smaller screens
                '@media (max-width: 768px)': {
                  gap: 1,
                },
              }}
            >
              <Typography
                variant='h6'
                sx={{
                  // Responsive typography for iPad
                  '@media (max-width: 1024px)': {
                    fontSize: '1rem',
                  },
                }}
                fontWeight='bold'
              >
                Select Customer
              </Typography>
              <Box
                sx={{
                  display: 'flex',
                  bgcolor: 'background.paper',
                  border: '1px solid',
                  borderColor: 'divider',
                  borderRadius: 4,
                  overflow: 'hidden',
                  // Responsive sizing for smaller screens
                  '@media (max-width: 768px)': {
                    minWidth: 'auto',
                    flexShrink: 0,
                  },
                }}
              >
                <Box
                  sx={{
                    px: 2,
                    py: 0.5,
                    cursor: 'pointer',
                    bgcolor: useAlternateUI ? 'primary.main' : 'transparent',
                    color: useAlternateUI ? 'white' : 'text.secondary',
                    transition: 'all 0.2s ease',
                    fontWeight: 'medium',
                    fontSize: '0.875rem',
                    '&:hover': {
                      bgcolor: useAlternateUI ? 'primary.main' : 'action.hover',
                    },
                  }}
                  onClick={() => setUseAlternateUI(true)}
                >
                  Code
                </Box>
                <Box
                  sx={{
                    px: 2,
                    py: 0.5,
                    cursor: 'pointer',
                    bgcolor: !useAlternateUI ? 'primary.main' : 'transparent',
                    color: !useAlternateUI ? 'white' : 'text.secondary',
                    transition: 'all 0.2s ease',
                    fontWeight: 'medium',
                    fontSize: '0.875rem',
                    '&:hover': {
                      bgcolor: !useAlternateUI
                        ? 'primary.main'
                        : 'action.hover',
                    },
                  }}
                  onClick={() => setUseAlternateUI(false)}
                >
                  Keypad
                </Box>
              </Box>
            </Box>

            {/* Customer Search Box */}
            <Box
              display='flex'
              alignItems='center'
              justifyContent='space-between'
              sx={{
                width: '100%',
                height: 80,
                bgcolor: '#f8f9fa',
                borderRadius: 2,
                px: 3,
                mb: 2,
                border: '1px solid #e0e0e0',
              }}
            >
              <Typography
                variant='h4'
                fontWeight='bold'
                display='flex'
                alignItems='center'
                flex={1}
              >
                {customerSearch}
              </Typography>
              {customerSearch && (
                <CompactClearButton
                  onClick={handleScreenKeypadClear}
                  sx={{ ml: 2 }}
                >
                  Clear
                </CompactClearButton>
              )}
            </Box>

            <GradientDivider
              text='RESULTS'
              containerProps={{ sx: { mt: 2, width: '100%' } }}
            />

            {/* Show create new customer button when no matches found */}
            {customerSearch && filteredCustomers.length === 0 && (
              <Box
                sx={{
                  p: 2,
                  bgcolor: 'background.paper',
                  border: '1px solid',
                  borderColor: 'primary.main',
                  borderRadius: 2,
                  mt: 2,
                  mb: 2,
                }}
              >
                <Typography variant='body2' color='text.secondary' gutterBottom>
                  No customer found with code &apos;{customerSearch}&apos;
                </Typography>
                <Button
                  variant='contained'
                  size='small'
                  onClick={() => handleCreateNewCustomer(customerSearch)}
                  startIcon={<AddIcon />}
                  sx={{ mt: 1 }}
                >
                  Create New Customer ({customerSearch.toUpperCase()})
                </Button>
              </Box>
            )}

            {useAlternateUI ? (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 2,
                  maxHeight: '600px',
                  overflowY: 'auto',
                  pr: 1,
                  '&::-webkit-scrollbar': {
                    width: '6px',
                  },
                  '&::-webkit-scrollbar-track': {
                    backgroundColor: '#f1f1f1',
                    borderRadius: '3px',
                  },
                  '&::-webkit-scrollbar-thumb': {
                    backgroundColor: '#c1c1c1',
                    borderRadius: '3px',
                    '&:hover': {
                      backgroundColor: '#a8a8a8',
                    },
                  },
                }}
              >
                {groupCustomersByLength(allCustomers).map(category => (
                  <Box key={category.label}>
                    <GradientDivider
                      text={category.label}
                      hideLeft
                      containerProps={{ sx: { mb: 1, width: '100%' } }}
                    />
                    <Box
                      sx={{
                        display: 'grid',
                        gridTemplateColumns:
                          category.label === '4+ Chars'
                            ? 'repeat(auto-fill, minmax(90px, 1fr))'
                            : 'repeat(auto-fill, minmax(60px, 1fr))',
                        gap: 0.5,
                        width: '100%',
                      }}
                    >
                      {category.options.map(option => (
                        <Typography
                          key={option.customer_id || option.id}
                          variant='body2'
                          sx={{
                            border: `${option.borderWidth || 1}px solid ${
                              option.isNew
                                ? '#4caf50'
                                : option.borderColor || '#DADADA'
                            }`,
                            borderRadius: 3,
                            px: 1,
                            py: 0.5,
                            cursor: option.disabled ? 'not-allowed' : 'pointer',
                            textAlign: 'center',
                            fontSize: '0.75rem',
                            fontWeight: 'bold', // Make customer short codes bold
                            opacity: option.disabled ? 0.5 : 1,
                            position: 'relative',

                            backgroundColor: (() => {
                              if (option.disabled) return 'grey.200';
                              const optionId = option.customer_id || option.id;
                              if (currentDetail?.customer_id === optionId) {
                                return option.isNew
                                  ? '#4caf50'
                                  : 'primary.main';
                              }
                              return option.isNew
                                ? '#e8f5e9'
                                : 'background.paper';
                            })(),
                            color: (() => {
                              if (option.disabled) return 'text.disabled';
                              const optionId = option.customer_id || option.id;
                              if (currentDetail?.customer_id === optionId) {
                                return 'white';
                              }
                              return option.isNew ? '#2e7d32' : 'text.primary';
                            })(),
                            '&:hover': {
                              backgroundColor: (() => {
                                if (option.disabled) return 'grey.200';
                                const optionId =
                                  option.customer_id || option.id;
                                // Special handling for new customers (ID -4)

                                return currentDetail?.customer_id === optionId
                                  ? 'primary.main'
                                  : 'action.hover';
                              })(),
                            },
                          }}
                          onClick={() => {
                            if (!option.disabled) {
                              handleCustomer(
                                option.customer_id || option.id,
                                option.customer_short_code || option.label
                              );
                            }
                          }}
                        >
                          {option.customer_short_code}
                        </Typography>
                      ))}
                    </Box>
                  </Box>
                ))}
              </Box>
            ) : (
              <Box sx={{ width: 320 }}>
                <Box
                  display='flex'
                  alignItems='center'
                  px={2}
                  sx={{
                    minHeight: expandedCustomer ? 'auto' : 60,
                    borderRadius: 2,
                    flexWrap: 'wrap',
                    alignItems: 'center',
                    gap: 1,
                    p: 1,
                    my: 2,
                    bgcolor: '#098D831A',
                  }}
                >
                  {!customerSearch ? (
                    <Typography
                      color='text.secondary'
                      variant='body1'
                      width='100%'
                      textAlign='center'
                    >
                      Start typing to see results...
                    </Typography>
                  ) : filteredCustomers.length === 0 ? (
                    <Typography
                      color='text.secondary'
                      variant='body1'
                      width='100%'
                      textAlign='center'
                    >
                      No results found
                    </Typography>
                  ) : !expandedCustomer ? (
                    <Box
                      sx={{
                        display: 'flex',
                        gap: 1,
                        overflowX: 'auto',
                        width: '100%',
                        py: 1,
                        '&::-webkit-scrollbar': {
                          height: '6px',
                        },
                        '&::-webkit-scrollbar-track': {
                          backgroundColor: 'rgba(0,0,0,0.1)',
                          borderRadius: '3px',
                        },
                        '&::-webkit-scrollbar-thumb': {
                          backgroundColor: 'rgba(0,0,0,0.3)',
                          borderRadius: '3px',
                        },
                      }}
                    >
                      {filteredCustomers.map(customer => (
                        <Box
                          key={customer.id || customer.customer_id}
                          onClick={() => {
                            handleCustomer(
                              customer.customer_id || customer.id,
                              customer.customer_short_code || customer.name
                            );
                          }}
                          sx={{
                            p: 1,
                            borderRadius: 1,
                            bgcolor:
                              (customer.customer_id || customer.id) ===
                              currentDetail?.customer_id
                                ? '#098D83'
                                : 'white',
                            color:
                              (customer.customer_id || customer.id) ===
                              currentDetail?.customer_id
                                ? 'white'
                                : 'black',
                            whiteSpace: 'nowrap',
                            flexShrink: 0,
                            cursor: 'pointer',
                            border: '1px solid #e0e0e0',
                            '&:hover': {
                              backgroundColor:
                                (customer.customer_id || customer.id) ===
                                currentDetail?.customer_id
                                  ? '#098D83'
                                  : '#f5f5f5',
                            },
                          }}
                        >
                          <Typography variant='body1' fontWeight='bold'>
                            {customer.customer_short_code || customer.name}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  ) : (
                    <Box
                      sx={{
                        display: 'flex',
                        gap: 1,
                        overflowX: 'auto',
                        width: '100%',
                        py: 1,
                        '&::-webkit-scrollbar': {
                          height: '6px',
                        },
                        '&::-webkit-scrollbar-track': {
                          backgroundColor: 'rgba(0,0,0,0.1)',
                          borderRadius: '3px',
                        },
                        '&::-webkit-scrollbar-thumb': {
                          backgroundColor: 'rgba(0,0,0,0.3)',
                          borderRadius: '3px',
                        },
                      }}
                    >
                      {filteredCustomers.map(customer => (
                        <Box
                          key={customer.id || customer.customer_id}
                          onClick={() => {
                            handleCustomer(
                              customer.customer_id || customer.id,
                              customer.customer_short_code || customer.name
                            );
                            toggleCustomerSearchAndKeypad();
                          }}
                          sx={{
                            p: 1,
                            borderRadius: 1,
                            bgcolor:
                              (customer.customer_id || customer.id) ===
                              currentDetail?.customer_id
                                ? '#098D83'
                                : 'white',
                            color:
                              (customer.customer_id || customer.id) ===
                              currentDetail?.customer_id
                                ? 'white'
                                : 'black',
                            whiteSpace: 'nowrap',
                            flexShrink: 0,
                            cursor: 'pointer',
                            border: '1px solid #e0e0e0',
                            '&:hover': {
                              backgroundColor:
                                (customer.customer_id || customer.id) ===
                                currentDetail?.customer_id
                                  ? '#098D83'
                                  : '#f5f5f5',
                            },
                          }}
                        >
                          <Typography variant='body1' fontWeight='bold'>
                            {customer.customer_short_code || customer.name}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  )}
                </Box>
                {expandedKeypad ? (
                  <ScreenKeypad
                    onClear={handleScreenKeypadClear}
                    onChange={handleScreenKeypadClick}
                    value={customerSearch}
                    showClear={false}
                  />
                ) : (
                  <Box
                    position='sticky'
                    bottom={0}
                    display='flex'
                    alignItems='center'
                    justifyContent='flex-end'
                    gap={1}
                    onClick={() => {
                      toggleCustomerSearchAndKeypad();
                    }}
                  >
                    <Box
                      sx={{
                        border: '1px solid #DADADA',
                        borderRadius: 3,
                        p: 1,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                      }}
                    >
                      <Typography variant='body2' fontWeight='bold'>
                        view keypad
                      </Typography>
                      <DialpadIcon />
                    </Box>
                  </Box>
                )}
              </Box>
            )}
          </Box>
        </Box>
      )}
      <Footer
        handleCancel={handleCancel}
        handleBack={handleBack}
        handleNext={handleNext}
        showBackButton={showSummary || selectedIndex !== 0}
        isSummary={showSummary}
        loading={loading}
      />
    </Box>
  );
};

export default AuctionDetailCapture;

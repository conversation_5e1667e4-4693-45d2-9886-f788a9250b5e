import { ArrowBack } from '@mui/icons-material';
import { Box, Button, Paper } from '@mui/material';
import AppButton from 'Components/AppButton';

const Footer = ({
  handleNext,
  handleCancel,
  handleBack,
  showBackButton,
  isSummary,
  loading = false,
}) => (
  <Paper
    elevation={2}
    sx={{
      p: 2,
      position: 'sticky',
      bottom: 0,
      zIndex: 1000,
      backgroundColor: 'white',
      borderTop: '1px solid #e0e0e0',
      // iPad specific styling
      '@media (max-width: 1024px)': {
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        width: '100%',
        boxSizing: 'border-box',
        boxShadow: '0 -2px 8px rgba(0,0,0,0.1)',
      },
      // Ensure buttons are always visible on smaller screens
      '@media (max-height: 600px)': {
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        width: '100%',
        boxSizing: 'border-box',
      },
    }}
  >
    <Box
      display='flex'
      justifyContent='space-between'
      alignItems='center'
      sx={{
        // Responsive button sizing for iPad
        '@media (max-width: 1024px)': {
          flexWrap: 'wrap',
          gap: 1,
        },
      }}
    >
      <Box>
        {showBackButton && (
          <Button
            size='large'
            variant='outlined'
            startIcon={<ArrowBack />}
            onClick={handleBack}
            sx={{
              // iPad responsive sizing
              '@media (max-width: 1024px)': {
                minWidth: '100px',
                fontSize: '0.875rem',
              },
            }}
          >
            Back
          </Button>
        )}
      </Box>
      <Box
        sx={{
          display: 'flex',
          gap: 1,
          // Ensure buttons stay on same line on iPad
          '@media (max-width: 1024px)': {
            flexShrink: 0,
          },
        }}
      >
        <Button
          size='large'
          variant='contained'
          color='inherit'
          onClick={handleCancel}
          sx={{
            // iPad responsive sizing
            '@media (max-width: 1024px)': {
              minWidth: '100px',
              fontSize: '0.875rem',
            },
          }}
        >
          Cancel
        </Button>
        <AppButton
          size='large'
          variant='contained'
          onClick={handleNext}
          sx={{
            ml: 1,
            // iPad responsive sizing
            '@media (max-width: 1024px)': {
              minWidth: '120px',
              fontSize: '0.875rem',
            },
          }}
          loading={loading}
          disabled={loading}
        >
          {isSummary ? 'Submit' : 'Save and Next'}
        </AppButton>
      </Box>
    </Box>
  </Paper>
);

export default Footer;

import { CUSTOMERS } from 'Utilities/constants';

export const categorizeCustomerShortCodes = customers => {
  // Ensure customers is an array before spreading
  const customerArray = Array.isArray(customers) ? customers : [];

  // Create groups object to store customers by first letter
  const allCustomers = [
    ...customerArray,
    CUSTOMERS.FRUIT_X,
    CUSTOMERS.VEGROW,
    CUSTOMERS.RETURN_TO_FARMER,
  ];
  const groups = {};

  // Group customers by first letter of short_code
  allCustomers.forEach(customer => {
    // Use customer_short_code if available, otherwise use customer_temp_short_code, then name
    const name =
      customer.customer_short_code ||
      customer.customer_temp_short_code ||
      customer.name;
    const firstLetterMatch = name.match(/[A-Za-z]/);
    const firstLetter = firstLetterMatch
      ? firstLetterMatch[0].toUpperCase()
      : '';

    if (!groups[firstLetter]) {
      groups[firstLetter] = [];
    }
    groups[firstLetter].push({
      value: customer.customer_id || customer.id,
      label:
        customer.customer_short_code ||
        customer.customer_temp_short_code ||
        customer.name,
    });
  });

  // Sort options within each group by short_code
  Object.keys(groups).forEach(key => {
    groups[key].sort((a, b) => a.label.localeCompare(b.label));
  });

  // Create final array of objects with letters A-Z
  const result = [];
  for (let i = 65; i <= 90; i += 1) {
    const letter = String.fromCharCode(i);
    if (groups[letter]) {
      result.push({
        label: letter,
        options: groups[letter],
      });
    }
  }

  return result;
};

export const getCustomerShortCodes = customers => {
  // Ensure customers is an array before spreading
  const customerArray = Array.isArray(customers)
    ? customers.map(customer => ({
        ...customer,
        disabled: false,
      }))
    : [];

  const allCustomers = [
    ...customerArray,
    CUSTOMERS.FRUIT_X,
    CUSTOMERS.VEGROW,
    CUSTOMERS.RETURN_TO_FARMER,
  ];

  return allCustomers;
};

export const formatLotsData = (data, skuList) => {
  if (Object.keys(data).length) {
    const response = Object.values(data)[0];
    const lots = response.lots_data.map(lot => {
      return {
        netWeight: lot.net_weight,
        units: lot.units,
        id: lot.id,
        chute: lot.chute,
        packaging_cost_per_kg: lot.packaging_cost_per_kg,
        auctioneer_id: lot.auctioneer_id,
        customer_short_code:
          lot.customer_id === -5
            ? lot.dcl_breached_customer_short_code
            : lot.customer_short_code,
        customer_temp_short_code: lot.customer_temp_short_code, // Added to preserve temp short code
        price: String(lot.selling_price_per_unit),
        sku_id: lot.sku_id,
        mandi_number_id: lot.mandi_number?.id || 0,
        mandi_number: lot.mandi_number?.mandi_number || '',
        grade: lot.mandi_number?.grade,
        mandi_sku_pack_net_weight_per_unit:
          lot.mandi_sku_pack_net_weight_per_unit || 0,
        discount_per_unit: lot.discount_per_unit || 0,
        customer_id:
          lot.customer_id === -5
            ? lot.dcl_breached_customer_id
            : lot.customer_id,
        no_of_units: lot.gatein_units,
        pack_type: lot.pack_type,
        gatein_product_name: lot.gatein_product_name,
        vehicle_number: lot.vehicle_number,
        // Find SKU info once to avoid duplicate operations
        ...(() => {
          const skuInfo = skuList?.find(sku => sku.id === lot.sku_id);
          return {
            sku_size_name: skuInfo?.sku_size_name,
            percentage: skuInfo?.price_percentage,
          };
        })(),
      };
    });
    return lots;
  }
  return [];
};

export const formatMandiNumberData = (data, lotsData) => {
  if (Object.keys(data).length) {
    const response = Object.values(data)[0];
    const lots = Object.values(response.mandi_number_details).map(
      mandiNumber => ({
        mandiNumber: mandiNumber.mandi_number,
        price: String(mandiNumber.mandi_number.price) || '0',
        total_price: String(mandiNumber.mandi_number.price) || '0',
        farmer_marka: mandiNumber.mandi_number.farmer_marka,
        vehicle_number: mandiNumber.mandi_number.vehicle_number,

        ...(() => {
          const lots =
            lotsData?.filter(lot =>
              mandiNumber.auction_lot_ids?.includes(lot.id)
            ) || [];

          mandiNumber.mandi_number.packType = lots[0]?.pack_type;
          mandiNumber.mandi_number.units = lots[0]?.no_of_units;
          mandiNumber.mandi_number.gatein_product_name =
            lots[0]?.gatein_product_name;
          return {
            lots,
            customer_id: lots[0]?.customer_id || 0,
            customer_short_code: lots[0]?.customer_short_code || '',
            units: lots[0]?.no_of_units,
            packType: lots[0]?.pack_type,
            gatein_product_name: lots[0]?.gatein_product_name,
            vehicle_number: lots[0]?.vehicle_number,
          };
        })(),
      })
    );

    return lots;
  }
  return [];
};

// Calculate price based on mandi selling price and SKU percentage
export const calculateSkuPrice = (mandiSellingPrice, pricePercentage) => {
  if (!mandiSellingPrice || !pricePercentage) return 0;
  return (parseFloat(mandiSellingPrice) * parseFloat(pricePercentage)) / 100;
};

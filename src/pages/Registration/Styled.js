import { Grid } from '@mui/material';
import styled from 'styled-components';

export const FieldWrapper = styled.div`
  padding: 1rem;
  .registration_category {
    flex-direction: column;
  }
`;

export const GridContainer = styled(Grid)`
  padding: ${props => props.theme.spacing(1)};

  & > div {
    width: 32%;
    margin: ${props => props.theme.spacing(1, 0.5)};
  }

  ${props => props.theme.breakpoints.down('md')} {
    & > div {
      width: 47%;
      margin: ${props => props.theme.spacing(1)};
    }
  }

  ${props => props.theme.breakpoints.down('md')} {
    & > div {
      width: 100%;
      margin: ${props => props.theme.spacing(1)};
    }
  }
`;

export const ImageListWrapper = styled.div`
  display: flex;
  padding-bottom: ${props => props.theme.spacing(0.5)};
  flex-wrap: wrap;
  max-width: 100%;
  gap: ${props => props.theme.spacing(1)};
`;

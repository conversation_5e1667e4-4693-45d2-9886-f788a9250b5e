import { makeStyles } from '@mui/styles';

const useAtomicStyles = makeStyles(() => ({
  border2: {
    border: '2px solid black',
  },
  borderRight2: {
    borderRight: '2px solid black',
  },
  borderBottom2: {
    borderBottom: '2px solid black !important',
  },
  borderBottom3: {
    borderBottom: '3px solid black',
  },
  marginLeft2: {
    marginLeft: '2px',
  },
  marginTop2: {
    marginTop: '2px',
  },
  marginTop6: {
    marginTop: '6px',
  },
  padding2: {
    padding: '2px',
  },
  padding10: {
    padding: '10px !important',
  },
  padding22: {
    padding: '22px !important',
  },
  paddingLeft2: {
    padding: '2px',
  },
  fontSize20: {
    fontSize: '24px',
  },
  boldFont: {
    fontWeight: 'bold',
  },
  flexRow: {
    display: 'flex',
  },
  flexColumn: {
    display: 'flex',
    flexDirection: 'column',
  },
  centerAlign: {
    alignItems: 'center',
  },
  justifySpaceBetween: {
    justifyContent: 'space-between',
  },
  font12: {
    fontSize: '12px',
  },
  minHeight22: {
    minHeight: '22px',
  },
  colorRed: {
    color: '#E53E3E', // Updated to use modern red
  },
  marginTop4: {
    marginTop: '4px',
  },
  marginTop12: {
    marginTop: '12px',
  },
  justifyCenter: {
    justifyContent: 'center',
  },
  font16: {
    fontSize: '16px',
  },
  font14: {
    fontSize: '14px',
  },
  textCenter: {
    textAlign: 'center',
  },
  textRight: {
    textAlign: 'right',
  },
  marginRight2: {
    marginRight: '2px',
  },
  marginTop8: {
    marginTop: '8px',
  },
  summaryFont: {
    fontSize: '18px',
    fontWeight: 'bold',
  },
  lightRed: {
    background: '#FED7D7', // Updated to modern light red
  },
  lightYellow: {
    background: '#FEFCBF', // Updated to modern light yellow
  },
  lightGreen: {
    background: '#F0FFF4', // Updated to modern light green
  },
  green_3: {
    background: '#E6FFFA', // Updated to modern light teal
  },
  green_4: {
    color: '#38A169', // Updated to modern green
  },
  red_1: {
    background: '#FED7D7', // Updated to modern light red
  },
  red_2: {
    color: '#E53E3E', // Updated to modern red
  },
  yellow_1: {
    color: '#D69E2E', // Updated to modern yellow
  },
  lightGray: {
    background: '#F7FAFC', // Updated to modern light gray
  },
  grayBorder: {
    border: '2px #E2E8F0 solid', // Updated to modern gray border
  },
  grayText: {
    color: '#000000', // Changed to black text
  },
  // New modern utility classes
  modernCard: {
    background: '#FFFFFF',
    borderRadius: '12px',
    boxShadow:
      '0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.24)',
    border: '1px solid #E2E8F0',
  },
  modernButton: {
    borderRadius: '8px',
    fontWeight: 500,
    textTransform: 'none',
    padding: '8px 16px',
  },
  primaryText: {
    color: '#2D3748',
  },
  secondaryText: {
    color: '#4A5568',
  },
  accentBackground: {
    background: '#F7FAFC',
  },
  successColor: {
    color: '#38A169',
  },
  warningColor: {
    color: '#D69E2E',
  },
  errorColor: {
    color: '#E53E3E',
  },
  infoColor: {
    color: '#3182CE',
  },
  positionRelative: {
    position: 'relative',
  },
}));

export default useAtomicStyles;

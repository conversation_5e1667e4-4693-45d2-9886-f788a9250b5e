import { createTheme, responsiveFontSizes } from '@mui/material/styles';

const muiTheme = createTheme({
  spacing: factor => `${factor * 8}px`,
  palette: {
    primary: {
      main: '#993333', // Keep the original red
      dark: '#800000', // Keep the original dark red
      light: '#CC6666', // Lighter version of the original red
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: '#38B2AC', // Refreshing teal/cyan
      dark: '#2C7A7B', // Darker teal
      light: '#81E6D9', // Light teal
      contrastText: '#FFFFFF',
    },
    background: {
      default: '#FAFAFA', // Clean light gray background
      paper: '#FFFFFF', // Pure white for cards/papers
      accent: '#F7FAFC', // Subtle accent background
    },
    text: {
      primary: '#000000', // Black for primary text
      secondary: '#000000', // Black for secondary text
      disabled: '#000000', // Black for disabled text
      hint: '#000000', // Black for hints
      green: '#38A169', // Fresh green
      yellow: '#D69E2E', // Warm yellow
      red: '#993333', // Consistent with primary red
      britishYellow: '#ECC94B', // Softer yellow variant
    },
    success: {
      main: '#38A169', // Fresh green for success states
      light: '#9AE6B4',
      dark: '#2F855A',
    },
    warning: {
      main: '#D69E2E', // Warm amber for warnings
      light: '#F6E05E',
      dark: '#B7791F',
    },
    error: {
      main: '#993333', // Consistent red for errors
      light: '#CC6666',
      dark: '#800000',
    },
    info: {
      main: '#3182CE', // Clean blue for info
      light: '#90CDF4',
      dark: '#2C5282',
    },
    divider: '#E2E8F0', // Subtle divider color
    grey: {
      50: '#F7FAFC',
      100: '#EDF2F7',
      200: '#E2E8F0',
      300: '#CBD5E0',
      400: '#A0AEC0',
      500: '#718096',
      600: '#4A5568',
      700: '#2D3748',
      800: '#1A202C',
      900: '#171923',
    },
  },
  inherit: {
    contrastText: '#FFFFFF',
  },
  typography: {
    fontFamily: [
      '"Inter"',
      '"Noto Sans"',
      'system-ui',
      '-apple-system',
      'sans-serif',
    ],
    textTransform: 'none',
    fontSize: 14, // Slightly larger base font size for better readability
    fontWeightLight: 300,
    fontWeightRegular: 400,
    fontWeightMedium: 500,
    fontWeightBold: 600,
    h1: {
      fontWeight: 600,
      fontSize: '2.5rem',
      lineHeight: 1.2,
    },
    h2: {
      fontWeight: 600,
      fontSize: '2rem',
      lineHeight: 1.3,
    },
    h3: {
      fontWeight: 600,
      fontSize: '1.5rem',
      lineHeight: 1.4,
    },
    h4: {
      fontWeight: 500,
      fontSize: '1.25rem',
      lineHeight: 1.4,
    },
    h5: {
      fontWeight: 500,
      fontSize: '1.125rem',
      lineHeight: 1.5,
    },
    h6: {
      fontWeight: 500,
      fontSize: '1rem',
      lineHeight: 1.5,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.5,
    },
    button: {
      fontWeight: 500,
      textTransform: 'none',
      letterSpacing: '0.02em',
    },
    caption: {
      fontSize: '0.75rem',
      lineHeight: 1.4,
    },
  },
  colors: {
    // Refreshing and modern color palette
    lightBlue: '#EBF8FF', // Very light blue
    lightGreen: '#F0FFF4', // Very light green
    lightGray: '#F7FAFC', // Clean light gray
    yellow: '#ECC94B', // Modern yellow
    gray: '#E2E8F0', // Subtle gray
    redPrimary: '#993333', // Keep original primary red
    redSecondary: '#800000', // Keep original secondary red
    lightRed: '#F5EBEB', // Light red background matching original
    lightCream: '#FFFAF0', // Warm cream
    lightYellow: '#FEFCBF', // Light yellow
    disabledText: '#A0AEC0', // Modern disabled text
    black: '#1A202C', // Rich black
    white: '#FFFFFF', // Pure white
    subtleGray: '#718096', // Subtle gray
    darkGray: '#4A5568', // Dark gray
    whitishGray: '#CBD5E0', // Light gray
    lightSapGreen: '#E6FFFA', // Very light teal
    whiteGreen: '#F0FFF4', // White green
    lightYellowL: '#FFFFF0', // Very light yellow
    darkYellow: '#D69E2E', // Dark yellow
    grayTextColor: '#2D3748', // Modern gray text
    lightPrimary: '#F5EBEB', // Light primary color matching original
    paleYellow: '#F7E98E', // Pale yellow
    mintCream: '#F0FFFF', // Mint cream
    // New refreshing colors
    sage: '#9CAF88', // Calming sage green
    lavender: '#E6E6FA', // Soft lavender
    peach: '#FFDBCC', // Warm peach
    sky: '#87CEEB', // Sky blue
    mint: '#98FB98', // Fresh mint
    coral: '#FF7F7F', // Soft coral
    seafoam: '#93E9BE', // Seafoam green
    powder: '#B0E0E6', // Powder blue
  },
  breakpoints: {
    values: {
      xs: 320,
      sm: 375,
      md: 768,
      lg: 1024,
      xl: 1440,
    },
  },
  shape: {
    borderRadius: 8, // Modern rounded corners
  },
  shadows: [
    'none',
    '0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.24)', // Subtle shadow
    '0px 3px 6px rgba(0, 0, 0, 0.16), 0px 3px 6px rgba(0, 0, 0, 0.23)', // Medium shadow
    '0px 10px 20px rgba(0, 0, 0, 0.19), 0px 6px 6px rgba(0, 0, 0, 0.23)', // Strong shadow
    '0px 14px 28px rgba(0, 0, 0, 0.25), 0px 10px 10px rgba(0, 0, 0, 0.22)', // Very strong shadow
    // Add more shadows as needed...
    ...Array(20).fill(
      '0px 14px 28px rgba(0, 0, 0, 0.25), 0px 10px 10px rgba(0, 0, 0, 0.22)'
    ),
  ],
  components: {
    // Modern component overrides for minimalistic design
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 500,
          padding: '8px 16px',
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
          },
        },
        contained: {
          '&:hover': {
            boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.15)',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow:
            '0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.24)',
          border: '1px solid #E2E8F0',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 8,
        },
        elevation1: {
          boxShadow:
            '0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.24)',
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
            '& fieldset': {
              borderColor: '#E2E8F0',
            },
            '&:hover fieldset': {
              borderColor: '#CBD5E0',
            },
            '&.Mui-focused fieldset': {
              borderColor: '#E53E3E',
              borderWidth: 2,
            },
          },
        },
      },
    },
    MuiBadge: {
      styleOverrides: {
        anchorOriginTopRightCircular: {
          transform: 'scale(0.8) translate(50%, -50%)',
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          fontWeight: 500,
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          borderRadius: 0, // Remove curved edges from header
          boxShadow:
            '0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.24)',
        },
      },
    },
  },
});

export default responsiveFontSizes(muiTheme);
